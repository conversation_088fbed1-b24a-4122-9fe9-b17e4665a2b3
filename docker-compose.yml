services:
  mysql:
    build:
      context: .
      dockerfile: Dockerfile-mysql
    ports:
      - "13306:3306"  # 修改为 13306 或其他未使用的端口
    networks:
      - app-network
    volumes:
      - mysql_data:/var/lib/mysql  # 持久化数据
    healthcheck:
      test: ["C<PERSON>", "mysqladmin", "ping", "-h", "localhost", "-u", "xiaozhi", "-p123456"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  node:
    build:
      context: .
      dockerfile: Dockerfile-node
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "8084:8084"
    networks:
      - app-network
    environment:
      - API_URL=http://server:8091

  server:
    build:
      context: .
      dockerfile: Dockerfile-server
      args:
        - VOSK_MODEL_SIZE=${VOSK_MODEL_SIZE:-small}
      # 添加构建缓存设置
      cache_from:
        - eclipse-temurin:21-jre
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "8091:8091"
    networks:
      - app-network
    environment:
      - SPRING_DATASOURCE_URL=*********************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=xiaozhi
      - SPRING_DATASOURCE_PASSWORD=123456
    volumes:
      - maven_repo:/root/.m2/repository  # 持久化Maven仓库
      - vosk_models:/vosk_cache  # 持久化Vosk模型
    restart: on-failure  # 添加重启策略，如果启动失败会自动重试

networks:
  app-network:
    driver: bridge

volumes:
  mysql_data:  # 定义持久化卷
  maven_repo:  # 持久化Maven仓库
  vosk_models:  # 持久化Vosk模型
include:
  - project: "cidevops/deploy"
    file: "jobs/deploy-k8s.yml"

variables:
  GIT_SUBMODULE_STRATEGY: recursive # 拉取 Submodule 内容
  #构建镜像
  PUB_CI_REGISTRY: registry.cn-beijing.aliyuncs.com
  CI_REGISTRY: registry-vpc.cn-beijing.aliyuncs.com
  CI_REGISTRY_USER: joylong19
  CI_REGISTRY_PASSWD: a01264e865beb0b7d1368ec8b1144630
  PUB_IMAGE_NAME: $PUB_CI_REGISTRY/pangtech/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA-$CI_PIPELINE_ID
  IMAGE_NAME: $CI_REGISTRY/pangtech/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA-$CI_PIPELINE_ID
  DOCKER_FILE_PATH: Dockerfile

  # 发布到k8s
  APP_NAME: $CI_PROJECT_NAME
  ACTIVE_ENV: uat
  SERVICE_PORT: 80
  CONTAINER_PORT: 8080

workflow:
  rules:
    - if: " $CI_COMMIT_BRANCH  == 'master' "
      when: always
    - if: " $CI_COMMIT_BRANCH  == 'test-9002' "
      when: always
    - if: " $CI_COMMIT_BRANCH  == 'test' "
      when: always
    - if: " $CI_COMMIT_BRANCH  == 'stage' "
      when: always

stages:
  - build
  - deploy

build:
  stage: build
  script:
    - if [ $CI_COMMIT_BRANCH == master ]; then
      export ACTIVE_ENV=prod;
      fi
    - if [ $CI_COMMIT_BRANCH == test-9002 ]; then
      export ACTIVE_ENV=test;
      fi
    - if [ $CI_COMMIT_BRANCH == test ]; then
      export ACTIVE_ENV=test;
      fi
    - if [ $CI_COMMIT_BRANCH == stage ]; then
      export ACTIVE_ENV=stage;
      fi
    - "[ -f Dockerfile ] || wget http://cidevops.lezhilong.cn/down/java21.dockerfile -O Dockerfile"
    - cat ${DOCKER_FILE_PATH}
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWD $CI_REGISTRY
    - docker build --build-arg APP_NAME=$APP_NAME --build-arg ACTIVE_ENV=$ACTIVE_ENV -t ${IMAGE_NAME} -f ${DOCKER_FILE_PATH} .
    - docker push ${IMAGE_NAME}
    - if [ $CI_COMMIT_BRANCH  == master ]; then
      docker rmi ${IMAGE_NAME};
      fi
  rules:
    - when: on_success

deploy_k8s:
  stage: deploy
  extends: .deploy-k8s
  rules:
    - if: " $CI_COMMIT_BRANCH  == 'master' "
      when: manual

deploy_test:
  stage: deploy
  script:
    - docker stop $APP_NAME && docker rm $APP_NAME
    - if [ -n "$EXPOSE_PORT" ]; then
        docker run -d -p $EXPOSE_PORT --name $APP_NAME --network ldyw ${IMAGE_NAME};
      else
        docker run -d -p 8884:8884/udp --name $APP_NAME --network ldyw ${IMAGE_NAME};
      fi
  only:
    - test
    - test-9002
  after_script:
    - wget http://cidevops.lezhilong.cn/down/gitlab_message.sh
    - bash gitlab_message.sh

deploy_stage:
  stage: deploy
  script:
    - docker stop stage.$APP_NAME && docker rm stage.$APP_NAME
    - docker run -d --name stage.$APP_NAME --hostname $APP_NAME --network ldyw.stage ${IMAGE_NAME}
  only:
    - stage
  after_script:
    - wget http://cidevops.lezhilong.cn/down/gitlab_message.sh
    - bash gitlab_message.sh

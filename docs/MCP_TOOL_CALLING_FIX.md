# MCP工具调用修复说明

## 问题发现

用户指出了一个关键问题：**`callToolOnDemand`不对，只是initialize了，并没有callTool**

经过分析，发现原来的实现确实有问题：

### 原始问题代码
```java
private Object callToolOnDemand(String endpointUrl, Map<String, String> headers, String toolName, Map<String, Object> params) {
    try (var client = McpClient.sync(transport).build()) {
        // Initialize connection
        var initializeResult = client.initialize();
        if (initializeResult == null) {
            return Map.of("error", "Failed to initialize MCP connection");
        }

        // ❌ 问题：只是初始化了连接，但没有实际调用工具
        logger.info("Tool {} called with params: {} on endpoint: {}", toolName, params, endpointUrl);
        return Map.of(
            "result", "Tool execution completed",  // ❌ 这只是一个占位符响应
            "toolName", toolName,
            "params", params,
            "endpoint", endpointUrl
        );
    }
}
```

### 问题分析

1. **McpClient API限制**: `McpClient`只提供了`initialize()`, `ping()`, `listTools()`等方法，没有直接的`callTool()`方法
2. **占位符响应**: 原来的实现只是返回了一个假的成功响应，实际上没有调用任何工具
3. **功能缺失**: 用户调用MCP工具时，得到的是假数据，而不是真正的工具执行结果

## 解决方案

### 技术方案选择

经过分析，决定采用**混合方案**：
- **McpClient**: 用于连接验证、工具发现和协议初始化
- **WebClient**: 用于实际的工具调用（通过JSON-RPC协议）

### 实现细节

#### 1. 保留McpClient用于工具发现
```java
// 在connectAndRegisterTools方法中，继续使用McpClient进行工具发现
try (var testClient = McpClient.sync(transport).build()) {
    var initializeResult = testClient.initialize();
    testClient.ping();
    var listToolsResult = testClient.listTools();
    // 创建工具回调...
}
```

#### 2. 使用WebClient进行实际工具调用
```java
private Object callToolOnDemand(String endpointUrl, Map<String, String> headers, String toolName, Map<String, Object> params) {
    try {
        // 获取已验证的连接信息
        ConnectionInfo connectionInfo = validatedEndpoints.get(endpointUrl);
        if (connectionInfo == null) {
            return Map.of("error", "Endpoint not validated");
        }
        
        // 创建WebClient用于JSON-RPC调用
        WebClient webClient = WebClient.builder()
                .baseUrl(endpointUrl)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
        
        // 添加存储的认证头
        connectionInfo.headers.forEach(webClient.mutate()::defaultHeader);
        
        // 创建JSON-RPC工具调用请求
        Map<String, Object> toolCallRequest = Map.of(
            "jsonrpc", "2.0",
            "id", System.currentTimeMillis(),
            "method", "tools/call",
            "params", Map.of(
                "name", toolName,
                "arguments", params
            )
        );
        
        // 发送请求并获取响应
        String responseJson = webClient.post()
                .bodyValue(toolCallRequest)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMillis(mcpConfig.getConnection().getTimeoutMs()))
                .block();
        
        // 解析JSON-RPC响应
        Map<String, Object> response = JsonUtil.fromJson(responseJson, Map.class);
        
        if (response.containsKey("error")) {
            logger.error("MCP tool call error: {}", response.get("error"));
            return Map.of("error", "MCP tool call failed: " + response.get("error"));
        }
        
        // 提取并返回实际结果
        Object result = response.get("result");
        if (result != null) {
            logger.info("MCP tool {} executed successfully", toolName);
            return result;
        } else {
            return Map.of("result", "Tool executed but no result returned");
        }

    } catch (Exception e) {
        logger.error("Error calling tool {} on MCP endpoint {}: {}", toolName, endpointUrl, e.getMessage(), e);
        return Map.of("error", "Failed to call tool: " + e.getMessage());
    }
}
```

## 技术优势

### 1. 最佳实践结合
- **McpClient**: 利用官方客户端的标准化工具发现和连接管理
- **WebClient**: 使用成熟的HTTP客户端进行实际的JSON-RPC调用

### 2. 协议兼容性
- **工具发现**: 使用MCP标准协议获取工具列表
- **工具调用**: 使用标准JSON-RPC 2.0协议调用工具
- **错误处理**: 正确处理JSON-RPC错误响应

### 3. 连接管理优化
- **连接验证**: 使用McpClient验证端点可用性
- **信息存储**: 将验证成功的连接信息存储在`ConnectionInfo`中
- **按需调用**: 工具调用时使用存储的连接信息

## 实际效果对比

### 修复前
```
用户: "帮我读取文件 /home/<USER>/document.txt"
MCP工具调用: mcp_read_file
返回结果: {
  "result": "Tool execution completed",  // ❌ 假数据
  "toolName": "read_file",
  "params": {"path": "/home/<USER>/document.txt"},
  "endpoint": "https://filesystem-mcp.example.com"
}
LLM响应: "工具执行完成" // ❌ 没有实际内容
```

### 修复后
```
用户: "帮我读取文件 /home/<USER>/document.txt"
MCP工具调用: mcp_read_file
JSON-RPC请求: {
  "jsonrpc": "2.0",
  "id": 1234567890,
  "method": "tools/call",
  "params": {
    "name": "read_file",
    "arguments": {"path": "/home/<USER>/document.txt"}
  }
}
返回结果: {
  "content": "这是文件的实际内容...",  // ✅ 真实数据
  "size": 1024,
  "lastModified": "2025-07-24T10:30:00Z"
}
LLM响应: "文件内容如下：这是文件的实际内容..." // ✅ 有实际内容
```

## 错误处理改进

### 1. 连接验证
```java
ConnectionInfo connectionInfo = validatedEndpoints.get(endpointUrl);
if (connectionInfo == null) {
    logger.warn("Endpoint {} not found in validated endpoints", endpointUrl);
    return Map.of("error", "Endpoint not validated");
}
```

### 2. JSON-RPC错误处理
```java
if (response.containsKey("error")) {
    logger.error("MCP tool call error: {}", response.get("error"));
    return Map.of("error", "MCP tool call failed: " + response.get("error"));
}
```

### 3. 超时处理
```java
.timeout(Duration.ofMillis(mcpConfig.getConnection().getTimeoutMs()))
```

## 日志改进

### 修复前
```
INFO [ThirdPartyMcpService] Tool read_file called with params: {path=/home/<USER>/document.txt} on endpoint: https://filesystem-mcp.example.com
```

### 修复后
```
INFO [ThirdPartyMcpService] Calling MCP tool read_file with params: {path=/home/<USER>/document.txt} on endpoint: https://filesystem-mcp.example.com
INFO [ThirdPartyMcpService] MCP tool read_file executed successfully
```

## 性能考虑

### 1. 连接复用
- 使用存储的`ConnectionInfo`避免重复验证
- WebClient支持连接池，提高并发性能

### 2. 超时控制
- 配置合理的超时时间，避免长时间等待
- 快速失败，提供用户友好的错误信息

### 3. 异常处理
- 详细的异常日志，便于问题排查
- 优雅的错误恢复，不影响其他工具调用

## 兼容性保证

### 1. API兼容性
- 公共接口保持不变
- 工具回调签名不变
- 错误响应格式兼容

### 2. 配置兼容性
- 继续使用现有的MCP配置
- 超时设置等参数保持兼容

### 3. 协议兼容性
- 符合MCP协议标准
- 符合JSON-RPC 2.0规范

## 测试验证

### 1. 单元测试
- 验证工具调用请求格式正确
- 验证响应解析逻辑正确
- 验证错误处理机制

### 2. 集成测试
- 测试与真实MCP端点的交互
- 验证工具调用结果的正确性
- 测试各种错误场景

### 3. 性能测试
- 测试工具调用的响应时间
- 验证并发调用的稳定性
- 监控内存和CPU使用情况

## 总结

这次修复解决了MCP工具调用的核心问题：

1. ✅ **修复了假工具调用**: 现在真正调用MCP端点的工具
2. ✅ **保持了架构优势**: 继续使用McpClient进行工具发现
3. ✅ **实现了真实功能**: 用户可以获得真正的工具执行结果
4. ✅ **改进了错误处理**: 提供详细的错误信息和日志
5. ✅ **保证了兼容性**: 不影响现有的API和配置

现在MCP工具可以真正为用户提供实用的功能，而不仅仅是占位符响应！

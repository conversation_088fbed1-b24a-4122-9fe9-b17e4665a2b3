# MCP工具注册功能

## 概述

本文档描述了在聊天开始时将数据库中可用的MCP（Model Context Protocol）注册到模型可用工具中的完整实现。

## 功能特性

### 1. 统一的MCP工具管理
- **设备端MCP工具**：支持从设备端获取和注册MCP工具
- **第三方MCP工具**：支持从数据库配置的第三方MCP端点获取和注册工具
- **统一注册服务**：提供统一的接口管理所有类型的MCP工具

### 2. 自动初始化
- 在聊天会话建立时自动初始化所有MCP工具
- 支持异步并行初始化，提高性能
- 错误隔离：单个MCP端点失败不影响其他端点

### 3. 灵活配置
- 通过数据库配置第三方MCP端点
- 支持认证token和自定义HTTP头
- 支持启用/禁用端点

## 架构设计

```
MessageHandler (聊天连接处理)
    ↓
McpToolRegistrationService (统一MCP工具注册服务)
    ├── DeviceMcpService (设备端MCP服务)
    └── ThirdPartyMcpManager (第三方MCP管理器)
        └── ThirdPartyMcpService (第三方MCP服务)
            └── SysMcpEndpointService (MCP端点数据库服务)
```

## 核心组件

### 1. McpToolRegistrationService
统一的MCP工具注册服务，负责协调设备端和第三方MCP工具的初始化。

**主要方法：**
- `initializeAllMcpTools(ChatSession)`: 初始化所有MCP工具
- `initializeDeviceMcpTools(ChatSession)`: 仅初始化设备端MCP工具
- `initializeThirdPartyMcpToolsOnly(ChatSession)`: 仅初始化第三方MCP工具
- `getMcpToolStats(ChatSession)`: 获取MCP工具统计信息

### 2. ThirdPartyMcpManager
第三方MCP端点管理器，负责管理多个第三方MCP端点的连接和工具获取。

**主要方法：**
- `initializeEnabledEndpoints()`: 初始化所有启用的端点
- `connectToEndpoint(String, Map<String, String>)`: 连接到指定端点
- `getAllTools()`: 获取所有第三方MCP工具
- `reloadEndpoints()`: 重新加载端点

### 3. ThirdPartyMcpService
第三方MCP服务，负责与外部MCP端点的底层通信。

**主要方法：**
- `connectAndRegisterTools(String, Map<String, String>)`: 连接并注册工具
- `disconnectFromEndpoint(String)`: 断开端点连接
- `isConnectedToEndpoint(String)`: 检查连接状态

## 数据库配置

### sys_mcp_endpoint表结构
```sql
CREATE TABLE `sys_mcp_endpoint` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `url` VARCHAR(512) NOT NULL COMMENT 'MCP端点URL',
  `name` VARCHAR(255) NOT NULL COMMENT '端点名称',
  `auth_token` VARCHAR(512) DEFAULT NULL COMMENT '认证令牌',
  `headers` TEXT DEFAULT NULL COMMENT '其他HTTP头信息（JSON格式）',
  `enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `del_flag` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_url` (`url`)
);
```

### 配置示例
```sql
INSERT INTO `sys_mcp_endpoint` (`url`, `name`, `auth_token`, `headers`, `enabled`) VALUES 
('https://api.example.com/mcp', '示例MCP端点', 'your-auth-token', '{"Custom-Header": "value"}', 1);
```

## 使用流程

### 1. 聊天会话建立
当用户建立聊天连接时，`MessageHandler.afterConnection()` 方法会被调用。

### 2. MCP工具初始化
在会话初始化过程中，系统会：
1. 调用 `McpToolRegistrationService.initializeAllMcpTools()`
2. 异步并行初始化设备端MCP和第三方MCP工具
3. 将获取到的工具注册到会话的 `ToolsSessionHolder` 中

### 3. 工具可用
注册完成后，AI模型可以调用这些MCP工具来执行各种任务。

## 配置参数

### application.yml配置
```yaml
xiaozhi:
  mcp:
    device:
      max-tools-count: 32      # 最大工具数量
      timeout-ms: 30000        # 设备超时时间
      auto-discovery: true     # 是否启用设备自动发现
    connection:
      timeout-ms: 10000        # 连接超时时间
      retry-count: 3           # 重试次数
      retry-interval-ms: 1000  # 重试间隔
```

## 错误处理

### 1. 连接失败处理
- 单个MCP端点连接失败不会影响其他端点
- 记录详细的错误日志便于调试
- 支持重试机制

### 2. 工具注册失败处理
- 工具注册失败会记录警告日志
- 不会中断整个初始化流程
- 提供统计信息显示成功/失败的工具数量

### 3. 异常隔离
- 设备端MCP和第三方MCP初始化相互独立
- 异步执行避免阻塞主流程

## 监控和调试

### 1. 日志记录
系统提供详细的日志记录，包括：
- MCP端点连接状态
- 工具注册成功/失败信息
- 性能统计信息

### 2. 统计信息
通过 `getMcpToolStats()` 方法可以获取：
- 总工具数量
- MCP工具数量
- 第三方MCP工具数量

### 3. 健康检查
- 支持检查MCP端点连接状态
- 支持重新加载端点配置

## 扩展性

### 1. 新增MCP端点类型
可以通过实现新的MCP服务类来支持不同类型的MCP端点。

### 2. 自定义工具处理
可以通过扩展 `McpToolRegistrationService` 来添加自定义的工具处理逻辑。

### 3. 配置热更新
支持在运行时重新加载MCP端点配置，无需重启服务。

## 测试

### 1. 单元测试
- `McpToolRegistrationServiceTest`: 测试MCP工具注册服务
- `ThirdPartyMcpManagerTest`: 测试第三方MCP管理器
- `ThirdPartyMcpServiceTest`: 测试第三方MCP服务

### 2. 集成测试
- `McpIntegrationTest`: 测试完整的MCP工具注册流程

### 3. 测试覆盖
- 正常流程测试
- 异常情况测试
- 边界条件测试

## 性能考虑

### 1. 异步初始化
设备端MCP和第三方MCP工具采用异步并行初始化，减少总体初始化时间。

### 2. 连接复用
第三方MCP服务复用WebClient连接，减少连接开销。

### 3. 缓存机制
ThirdPartyMcpManager缓存已连接的端点和工具，避免重复初始化。

## 安全考虑

### 1. 认证机制
支持Bearer token认证和自定义HTTP头认证。

### 2. 连接安全
支持HTTPS连接，确保数据传输安全。

### 3. 权限控制
通过数据库配置控制哪些MCP端点可以被使用。

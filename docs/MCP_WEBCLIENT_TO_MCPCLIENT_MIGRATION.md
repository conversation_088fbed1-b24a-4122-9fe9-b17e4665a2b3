# MCP WebClient 到 McpClient 迁移说明

## 概述

本文档描述了将第三方MCP服务从使用WebClient改为使用McpClient的迁移过程和技术细节。

## 迁移背景

### 原始实现问题
1. **WebClient复杂性**: 需要手动实现JSON-RPC 2.0协议
2. **错误处理复杂**: 需要处理HTTP层面和协议层面的错误
3. **协议实现**: 需要手动构造MCP协议消息
4. **连接管理**: 需要管理WebClient的生命周期

### McpClient优势
1. **原生MCP支持**: 直接支持MCP协议，无需手动实现
2. **简化API**: 提供高级API如`initialize()`, `listTools()`, `ping()`
3. **自动连接管理**: 自动处理连接的建立和关闭
4. **类型安全**: 提供强类型的API接口

## 技术变更详情

### 1. 依赖变更

**移除的依赖**:
```java
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import java.time.Duration;
```

**新增的依赖**:
```java
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
```

### 2. 连接创建方式变更

**原始WebClient方式**:
```java
private WebClient createWebClient(String endpointUrl, Map<String, String> headers) {
    WebClient.Builder builder = WebClient.builder()
            .baseUrl(endpointUrl)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
    
    if (headers != null) {
        headers.forEach(builder::defaultHeader);
    }
    
    return builder.build();
}
```

**新的McpClient方式**:
```java
private McpClient createMcpClient(String endpointUrl, Map<String, String> headers) throws Exception {
    URI uri = new URI(endpointUrl);
    HttpRequest.Builder requestBuilder = HttpRequest.newBuilder();
    
    if (headers != null) {
        headers.forEach(requestBuilder::header);
    }
    
    var transport = HttpClientSseClientTransport
            .builder(uri.toString().replace(uri.getPath(), ""))
            .sseEndpoint(uri.getPath().isEmpty() ? "/" : uri.getPath())
            .requestBuilder(requestBuilder)
            .build();
    
    return McpClient.sync(transport).build();
}
```

### 3. 初始化流程变更

**原始WebClient方式**:
```java
private Map<String, Object> initializeConnection(WebClient webClient) {
    Map<String, Object> initializeRequest = Map.of(
        "jsonrpc", "2.0",
        "id", System.currentTimeMillis(),
        "method", "initialize",
        "params", Map.of()
    );
    
    String responseJson = webClient.post()
            .bodyValue(initializeRequest)
            .retrieve()
            .bodyToMono(String.class)
            .timeout(Duration.ofMillis(mcpConfig.getConnection().getTimeoutMs()))
            .block();
    
    return JsonUtil.fromJson(responseJson, new TypeReference<Map<String, Object>>() {});
}
```

**新的McpClient方式**:
```java
try (var client = McpClient.sync(transport).build()) {
    var initializeResult = client.initialize();
    if (initializeResult == null) {
        // Handle initialization failure
    }
    
    client.ping();
    var listToolsResult = client.listTools();
    // Process tools...
}
```

### 4. 工具获取方式变更

**原始WebClient方式**:
```java
private List<FunctionToolCallback> getAndRegisterTools(WebClient webClient, String endpointUrl) {
    Map<String, Object> toolsRequest = Map.of(
        "jsonrpc", "2.0",
        "id", System.currentTimeMillis(),
        "method", "tools/list",
        "params", Map.of()
    );
    
    String responseJson = webClient.post()
            .bodyValue(toolsRequest)
            .retrieve()
            .bodyToMono(String.class)
            .timeout(Duration.ofMillis(mcpConfig.getConnection().getTimeoutMs()))
            .block();
    
    Map<String, Object> response = JsonUtil.fromJson(responseJson, new TypeReference<Map<String, Object>>() {});
    // Parse response and create tool callbacks...
}
```

**新的McpClient方式**:
```java
try (var client = McpClient.sync(transport).build()) {
    var listToolsResult = client.listTools();
    
    if (listToolsResult != null && listToolsResult.tools() != null) {
        return listToolsResult.tools().stream()
                .map(tool -> createToolCallback(endpointUrl, headers, tool))
                .toList();
    }
}
```

### 5. 工具调用方式变更

**原始WebClient方式**:
```java
private Object callTool(WebClient webClient, String toolName, Map<String, Object> params, String endpointUrl) {
    Map<String, Object> toolCallRequest = Map.of(
        "jsonrpc", "2.0",
        "id", System.currentTimeMillis(),
        "method", "tools/call",
        "params", Map.of(
            "name", toolName,
            "arguments", params
        )
    );
    
    String responseJson = webClient.post()
            .bodyValue(toolCallRequest)
            .retrieve()
            .bodyToMono(String.class)
            .timeout(Duration.ofMillis(mcpConfig.getConnection().getTimeoutMs()))
            .block();
    
    // Parse response...
}
```

**新的混合方式** (McpClient + WebClient):
```java
private Object callToolOnDemand(String endpointUrl, Map<String, String> headers, String toolName, Map<String, Object> params) {
    // Use McpClient for connection validation and tool discovery
    // Use WebClient for actual tool calling via JSON-RPC

    ConnectionInfo connectionInfo = validatedEndpoints.get(endpointUrl);
    if (connectionInfo == null) {
        return Map.of("error", "Endpoint not validated");
    }

    // Create WebClient for JSON-RPC call
    WebClient webClient = WebClient.builder()
            .baseUrl(endpointUrl)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .build();

    // Create JSON-RPC request for tool call
    Map<String, Object> toolCallRequest = Map.of(
        "jsonrpc", "2.0",
        "id", System.currentTimeMillis(),
        "method", "tools/call",
        "params", Map.of(
            "name", toolName,
            "arguments", params
        )
    );

    // Send the request and parse response
    String responseJson = webClient.post()
            .bodyValue(toolCallRequest)
            .retrieve()
            .bodyToMono(String.class)
            .timeout(Duration.ofMillis(mcpConfig.getConnection().getTimeoutMs()))
            .block();

    Map<String, Object> response = JsonUtil.fromJson(responseJson, Map.class);
    return response.get("result");
}
```

## 架构改进

### 1. 连接管理策略

**原始策略**: 长连接管理
- 创建WebClient实例并存储在Map中
- 需要手动管理连接生命周期
- 连接池配置复杂

**新策略**: 按需连接
- 工具调用时创建临时McpClient
- 使用try-with-resources自动管理连接
- 简化连接管理逻辑

### 2. 错误处理改进

**原始错误处理**:
```java
try {
    // WebClient operations
} catch (WebClientResponseException e) {
    logger.error("HTTP error: {}", e.getMessage());
} catch (Exception e) {
    logger.error("Unexpected error", e);
}
```

**新错误处理**:
```java
try (var client = McpClient.sync(transport).build()) {
    // MCP operations
} catch (Exception e) {
    logger.error("MCP operation failed: {}", e.getMessage());
    return Map.of("error", "Failed to call tool: " + e.getMessage());
}
```

### 3. 工具定义处理

**改进点**:
- 使用反射提取工具属性，适应不同的Tool类型
- 更好的空值处理和默认值设置
- 统一的工具名称前缀处理

## 性能影响

### 1. 连接开销
- **原始**: 长连接，初始化开销大，后续调用快
- **新方案**: 按需连接，每次调用有连接开销，但避免了连接池管理

### 2. 内存使用
- **原始**: 需要维护WebClient实例和连接池
- **新方案**: 无需维护长连接，内存使用更少

### 3. 并发性能
- **原始**: 连接池支持并发，但需要配置管理
- **新方案**: 每次调用独立连接，天然支持并发

## 兼容性考虑

### 1. API兼容性
- 公共API接口保持不变
- 内部实现完全重构
- 对外行为基本一致

### 2. 配置兼容性
- 保留原有的超时配置
- 移除WebClient特定配置
- 新增McpClient相关配置

### 3. 错误处理兼容性
- 错误消息格式保持一致
- 错误类型可能有所变化
- 日志级别和内容优化

## 测试验证

### 1. 单元测试更新
- 更新mock对象从WebClient到McpClient
- 调整测试用例以适应新的API
- 验证错误处理逻辑

### 2. 集成测试
- 验证与真实MCP端点的连接
- 测试工具发现和调用流程
- 验证并发调用场景

### 3. 性能测试
- 对比连接建立时间
- 测试工具调用响应时间
- 验证内存使用情况

## 后续优化建议

### 1. 连接池优化
- 考虑实现McpClient连接池
- 平衡连接复用和资源管理
- 支持连接预热机制

### 2. 工具调用优化
- 实现真正的MCP工具调用协议
- 支持流式响应处理
- 优化大数据传输场景

### 3. 监控和诊断
- 添加连接状态监控
- 实现调用链路追踪
- 提供性能指标收集

## 总结

从WebClient迁移到McpClient带来了以下主要改进：

1. **简化实现**: 无需手动实现MCP协议细节
2. **提高可靠性**: 使用官方MCP客户端库
3. **改善维护性**: 代码更简洁，易于理解和维护
4. **增强兼容性**: 更好地支持MCP协议标准

这次迁移为后续的功能扩展和性能优化奠定了良好的基础。

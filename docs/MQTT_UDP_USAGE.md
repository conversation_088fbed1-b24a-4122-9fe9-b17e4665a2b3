# MQTT+UDP ChatSession 使用指南

## 概述

本项目实现了基于 MQTT+UDP 的 ChatSession 系统，支持设备通过 MQTT 协议连接到服务器，并使用 UDP 协议传输加密的音频数据。

## 系统架构

```
设备 <--MQTT--> 服务器 <--UDP--> 设备
     (控制消息)        (音频数据)
```

### 核心组件

1. **MqttConfig** - MQTT和UDP配置管理
2. **MqttSession** - MQTT会话实现，继承自ChatSession
3. **MqttMessageHandler** - MQTT消息处理器
4. **MqttIntegrationConfig** - Spring Integration MQTT配置
5. **UdpServer** - UDP服务器，接收加密音频数据
6. **UdpSessionManager** - UDP会话管理和安全验证
7. **CryptoUtils** - AES-128-CTR加密工具

## 服务端口配置

- **MQTT服务器**: 端口 1883 (可配置)
- **UDP服务器**: 端口 8884 (可配置)

## 设备连接流程

### 1. MQTT连接
设备通过MQTT协议连接到服务器：
```
Topic: xiaozhi/{deviceId}/hello
```

### 2. Hello消息
设备发送hello消息，包含音频参数和特性：
```json
{
  "type": "hello",
  "transport": "mqtt",
  "deviceId": "device001",
  "audioParams": {
    "format": "opus",
    "sampleRate": 16000,
    "channels": 1,
    "frameDuration": 20
  },
  "features": {
    "mcp": false
  },
  "timestamp": 1640995200000
}
```

### 3. 服务器响应
服务器返回UDP连接参数：
```json
{
  "type": "hello",
  "version": 3,
  "sessionId": "mqtt-device001-1640995200000",
  "transport": "udp",
  "udp": {
    "server": "192.168.1.100",
    "port": 8884,
    "encryption": "aes-128-ctr",
    "key": "0123456789abcdef0123456789abcdef",
    "nonce": "fedcba9876543210fedcba9876543210"
  },
  "audioParams": {
    "format": "opus",
    "sampleRate": 16000,
    "channels": 1,
    "frameDuration": 20
  }
}
```

### 4. UDP音频传输
设备使用UDP发送加密的音频数据：

#### UDP数据包格式
```
+----------------+----------------+----------------+----------------+
|           Session ID (16 bytes)                                  |
+----------------+----------------+----------------+----------------+
|                    Sequence Number (8 bytes)                     |
+----------------+----------------+----------------+----------------+
|              Data Length (4 bytes)              |               |
+----------------+----------------+----------------+               |
|                    Encrypted Audio Data                          |
|                         (Variable)                               |
+----------------+----------------+----------------+----------------+
```

## 安全机制

### AES-128-CTR加密
- 每个会话使用唯一的128位密钥
- 16字节随机nonce
- CTR模式加密，支持流式处理

### 防重放攻击
- 序列号验证，拒绝重复或过期的数据包
- 滑动窗口机制，窗口大小1000

### 会话管理
- 会话超时自动清理（默认5分钟）
- 客户端地址验证
- 加密上下文隔离

## 配置说明

### application.properties
```properties
# MQTT Broker配置
mqtt.broker.url=tcp://localhost:1883
mqtt.broker.username=
mqtt.broker.password=
mqtt.broker.connection-timeout=30
mqtt.broker.keep-alive-interval=60
mqtt.broker.automatic-reconnect=true
mqtt.broker.clean-session=true

# MQTT客户端配置
mqtt.client.id=xiaozhi-server
mqtt.client.random-suffix=true

# Topic配置
mqtt.topic.prefix=xiaozhi
mqtt.topic.qos=1

# 会话管理配置
mqtt.session.timeout=300
mqtt.session.heartbeat-interval=60
mqtt.session.cleanup-interval=30

# UDP服务器配置
mqtt.udp.port=8884
mqtt.udp.host=0.0.0.0
mqtt.udp.receive-buffer-size=65536
mqtt.udp.send-buffer-size=65536
mqtt.udp.worker-threads=4
mqtt.udp.session-timeout-ms=300000

# 加密配置
mqtt.encryption.algorithm=AES-128-CTR
mqtt.encryption.key-length=16
mqtt.encryption.nonce-length=16
mqtt.encryption.sequence-window-size=1000
```

## API接口

### 监控接口

#### 获取系统统计
```bash
GET /api/mqtt/stats
```

#### 获取所有会话
```bash
GET /api/mqtt/sessions
```

#### 获取指定设备会话
```bash
GET /api/mqtt/session/{deviceId}
```

#### 关闭设备会话
```bash
POST /api/mqtt/session/{deviceId}/close
```

#### 发送测试消息
```bash
POST /api/mqtt/session/{deviceId}/test
Content-Type: application/json

{
  "message": "测试消息"
}
```

#### 获取UDP统计
```bash
GET /api/mqtt/udp/stats
```

#### 健康检查
```bash
GET /api/mqtt/health
```

## 客户端示例

### Python客户端示例
```python
import paho.mqtt.client as mqtt
import json
import socket
import struct
import time
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

class MqttUdpClient:
    def __init__(self, device_id, mqtt_host="localhost", mqtt_port=1883):
        self.device_id = device_id
        self.mqtt_host = mqtt_host
        self.mqtt_port = mqtt_port
        self.session_id = None
        self.udp_config = None
        self.udp_socket = None
        self.sequence = 0
        
        # MQTT客户端
        self.mqtt_client = mqtt.Client()
        self.mqtt_client.on_connect = self.on_mqtt_connect
        self.mqtt_client.on_message = self.on_mqtt_message
    
    def connect(self):
        # 连接MQTT
        self.mqtt_client.connect(self.mqtt_host, self.mqtt_port, 60)
        self.mqtt_client.loop_start()
        
        # 发送hello消息
        hello_msg = {
            "type": "hello",
            "transport": "mqtt",
            "deviceId": self.device_id,
            "audioParams": {
                "format": "opus",
                "sampleRate": 16000,
                "channels": 1,
                "frameDuration": 20
            },
            "timestamp": int(time.time() * 1000)
        }
        
        topic = f"xiaozhi/{self.device_id}/hello"
        self.mqtt_client.publish(topic, json.dumps(hello_msg))
    
    def on_mqtt_connect(self, client, userdata, flags, rc):
        print(f"MQTT连接成功: {rc}")
        # 订阅响应topic
        client.subscribe(f"xiaozhi/{self.device_id}/response")
    
    def on_mqtt_message(self, client, userdata, msg):
        try:
            response = json.loads(msg.payload.decode())
            if response.get("type") == "hello" and "udp" in response:
                self.session_id = response["sessionId"]
                self.udp_config = response["udp"]
                print(f"收到UDP配置: {self.udp_config}")
                self.setup_udp()
        except Exception as e:
            print(f"处理MQTT消息失败: {e}")
    
    def setup_udp(self):
        # 创建UDP套接字
        self.udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        print(f"UDP连接已建立: {self.udp_config['server']}:{self.udp_config['port']}")
    
    def send_audio_data(self, audio_data):
        if not self.udp_socket or not self.udp_config:
            print("UDP未初始化")
            return
        
        # 加密音频数据
        encrypted_data = self.encrypt_data(audio_data)
        
        # 构建UDP数据包
        packet = self.build_udp_packet(encrypted_data)
        
        # 发送UDP数据包
        self.udp_socket.sendto(packet, (self.udp_config['server'], self.udp_config['port']))
        self.sequence += 1
    
    def encrypt_data(self, data):
        key = bytes.fromhex(self.udp_config['key'])
        nonce = bytes.fromhex(self.udp_config['nonce'])
        
        # 构建CTR模式的IV
        iv = nonce[:12] + struct.pack('>I', self.sequence)
        
        cipher = Cipher(algorithms.AES(key), modes.CTR(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        return encryptor.update(data) + encryptor.finalize()
    
    def build_udp_packet(self, data):
        # Session ID (16字节)
        session_id_bytes = self.session_id.encode()[:16].ljust(16, b'\x00')
        
        # Sequence (8字节)
        sequence_bytes = struct.pack('>Q', self.sequence)
        
        # Data Length (4字节)
        length_bytes = struct.pack('>I', len(data))
        
        return session_id_bytes + sequence_bytes + length_bytes + data

# 使用示例
client = MqttUdpClient("device001")
client.connect()

# 发送音频数据
audio_data = b"fake_audio_data"
client.send_audio_data(audio_data)
```

## 故障排除

### 常见问题

1. **MQTT连接失败**
   - 检查MQTT Broker是否运行
   - 验证连接参数（URL、用户名、密码）

2. **UDP数据包丢失**
   - 检查防火墙设置
   - 验证UDP端口是否开放

3. **加密解密失败**
   - 确认密钥和nonce正确传输
   - 检查序列号是否同步

4. **会话超时**
   - 调整会话超时配置
   - 确保定期发送心跳

### 日志查看
```bash
# 查看MQTT相关日志
grep "MQTT" logs/application.log

# 查看UDP相关日志
grep "UDP" logs/application.log

# 查看加密相关日志
grep "Crypto" logs/application.log
```

## 性能优化

1. **UDP缓冲区调优**
   - 根据网络环境调整接收/发送缓冲区大小
   - 增加工作线程数处理高并发

2. **加密性能**
   - 使用硬件加速（如果可用）
   - 批量处理音频数据

3. **会话管理**
   - 合理设置会话超时时间
   - 定期清理过期会话

## 扩展开发

### 添加新的消息类型
1. 在MqttMessageHandler中添加处理逻辑
2. 更新Topic路由规则
3. 实现相应的响应格式

### 支持其他加密算法
1. 扩展CryptoUtils类
2. 更新配置选项
3. 修改UDP数据包格式（如需要）

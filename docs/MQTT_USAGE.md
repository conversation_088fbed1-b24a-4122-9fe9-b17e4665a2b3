# MQTT ChatSession 使用指南

## 概述

本项目基于 MQTT 协议实现了 ChatSession，支持设备通过 MQTT 连接到小智服务器进行语音对话。MQTT 相比 WebSocket 具有更好的网络适应性、自动重连能力和低功耗特性，特别适合 IoT 设备。

## 功能特性

- ✅ **MQTT 协议支持**: 基于 Eclipse Paho MQTT 客户端
- ✅ **自动重连**: 网络断开时自动重连
- ✅ **会话管理**: 自动会话超时检测和清理
- ✅ **心跳机制**: 定期心跳检测保持连接活跃
- ✅ **消息路由**: 支持文本、二进制、心跳等多种消息类型
- ✅ **设备管理**: 支持多设备同时连接
- ✅ **监控接口**: 提供 REST API 监控 MQTT 连接状态

## 配置说明

### 服务器配置

在 `application.properties` 中配置 MQTT 相关参数：

```properties
# MQTT Broker 配置
mqtt.broker.url=tcp://localhost:1883
mqtt.broker.username=
mqtt.broker.password=
mqtt.client.id=xiaozhi-server
mqtt.topic.prefix=xiaozhi

# 会话管理配置
mqtt.session.timeout=300          # 会话超时时间（秒）
mqtt.heartbeat.interval=60        # 心跳发送间隔（秒）
mqtt.session.cleanup.interval=30  # 会话清理检查间隔（秒）
```

### MQTT Broker 部署

推荐使用 Eclipse Mosquitto 作为 MQTT Broker：

```bash
# Docker 方式部署
docker run -it -p 1883:1883 -p 9001:9001 eclipse-mosquitto

# 或使用配置文件
docker run -it -p 1883:1883 -p 9001:9001 \
  -v /path/to/mosquitto.conf:/mosquitto/config/mosquitto.conf \
  eclipse-mosquitto
```

## Topic 设计

MQTT 消息采用以下 Topic 结构：

```
xiaozhi/{deviceId}/{messageType}
```

### 消息类型

| 消息类型 | Topic 示例 | 说明 |
|---------|-----------|------|
| `hello` | `xiaozhi/device001/hello` | 设备连接握手消息 |
| `text` | `xiaozhi/device001/text` | 文本消息（JSON格式） |
| `binary` | `xiaozhi/device001/binary` | 二进制消息（音频数据） |
| `heartbeat` | `xiaozhi/device001/heartbeat` | 心跳消息 |

## 消息格式

### Hello 消息

设备连接时发送的握手消息：

```json
{
  "type": "hello",
  "transport": "mqtt",
  "deviceId": "device001",
  "timestamp": 1640995200000
}
```

### 文本消息

用于发送语音识别结果或其他文本数据：

```json
{
  "type": "text",
  "content": "你好，小智",
  "timestamp": 1640995200000
}
```

### 心跳消息

保持连接活跃的心跳消息：

```json
{
  "type": "heartbeat",
  "timestamp": 1640995200000
}
```

## 客户端开发

### Java 客户端示例

参考 `examples/MqttClientExample.java`：

```java
// 连接到 MQTT 服务器
MqttClient mqttClient = new MqttClient(BROKER_URL, CLIENT_ID, new MemoryPersistence());
mqttClient.connect(connOpts);

// 订阅服务器消息
mqttClient.subscribe("xiaozhi/device001/text", 1);
mqttClient.subscribe("xiaozhi/device001/binary", 1);

// 发送 Hello 消息
String helloMessage = "{\"type\":\"hello\",\"deviceId\":\"device001\"}";
mqttClient.publish("xiaozhi/device001/hello", new MqttMessage(helloMessage.getBytes()));
```

### Python 客户端示例

```python
import paho.mqtt.client as mqtt
import json
import time

def on_connect(client, userdata, flags, rc):
    print(f"连接结果: {rc}")
    # 订阅服务器消息
    client.subscribe("xiaozhi/device001/text")
    client.subscribe("xiaozhi/device001/binary")

def on_message(client, userdata, msg):
    print(f"收到消息: {msg.topic} - {msg.payload.decode()}")

# 创建客户端
client = mqtt.Client("device001")
client.on_connect = on_connect
client.on_message = on_message

# 连接服务器
client.connect("localhost", 1883, 60)

# 发送 Hello 消息
hello_msg = {
    "type": "hello",
    "deviceId": "device001",
    "timestamp": int(time.time() * 1000)
}
client.publish("xiaozhi/device001/hello", json.dumps(hello_msg))

# 保持连接
client.loop_forever()
```

## 监控和管理

### REST API 接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/mqtt/stats` | GET | 获取 MQTT 会话统计信息 |
| `/api/mqtt/sessions` | GET | 获取所有 MQTT 会话列表 |
| `/api/mqtt/session/{deviceId}` | GET | 获取指定设备会话信息 |
| `/api/mqtt/session/{deviceId}/close` | POST | 强制关闭设备会话 |
| `/api/mqtt/session/{deviceId}/test` | POST | 发送测试消息到设备 |

### 使用示例

```bash
# 获取会话统计
curl http://localhost:8091/api/mqtt/stats

# 获取所有会话
curl http://localhost:8091/api/mqtt/sessions

# 获取指定设备会话
curl http://localhost:8091/api/mqtt/session/device001

# 关闭设备会话
curl -X POST http://localhost:8091/api/mqtt/session/device001/close

# 发送测试消息
curl -X POST "http://localhost:8091/api/mqtt/session/device001/test?message=hello"
```

## 最佳实践

### 1. 设备 ID 设计

- 使用唯一且有意义的设备 ID
- 建议格式：`{产品类型}-{序列号}` 如 `speaker-001`
- 避免使用特殊字符，推荐使用字母、数字、连字符

### 2. 消息质量等级 (QoS)

- 心跳消息：QoS 0（最多一次）
- 文本消息：QoS 1（至少一次）
- 音频数据：QoS 1（至少一次）

### 3. 错误处理

- 实现连接丢失重连机制
- 处理消息发送失败的重试逻辑
- 监控网络状态，适时调整心跳间隔

### 4. 性能优化

- 合理设置心跳间隔，平衡功耗和连接稳定性
- 大文件传输考虑分片发送
- 使用消息压缩减少网络传输量

## 故障排查

### 常见问题

1. **连接失败**
   - 检查 MQTT Broker 是否正常运行
   - 验证网络连接和防火墙设置
   - 确认用户名密码配置正确

2. **消息丢失**
   - 检查 QoS 设置
   - 确认 Topic 订阅是否正确
   - 查看服务器日志排查问题

3. **会话超时**
   - 调整心跳间隔和超时时间
   - 检查网络稳定性
   - 确认设备端心跳发送正常

### 日志查看

服务器端日志关键字：
- `MqttSession`: MQTT 会话相关日志
- `MqttMessageHandler`: 消息处理日志
- `MqttSessionManager`: 会话管理日志

## 扩展开发

### 自定义消息类型

1. 在 `MqttMessageHandler` 中添加新的消息类型处理
2. 定义相应的消息格式和 Topic 规则
3. 更新客户端代码支持新消息类型

### 集成其他协议

MQTT ChatSession 可以与 WebSocket ChatSession 并存，支持多种连接方式：

```java
// 统一的会话管理
sessionManager.registerSession(sessionId, mqttSession);
sessionManager.registerSession(sessionId, webSocketSession);
```

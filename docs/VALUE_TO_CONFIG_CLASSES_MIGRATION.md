# @Value 注解重构为配置类迁移指南

## 概述

本项目已将所有使用 `@Value` 注解的配置重构为 `@ConfigurationProperties` 配置类，以提供更好的类型安全、IDE 支持和配置管理。

## 迁移内容

### 1. 创建的配置类

#### ApplicationConfig
- **路径**: `src/main/java/com/xiaozhi/config/ApplicationConfig.java`
- **前缀**: `xiaozhi`
- **管理配置**: 服务器端口、文件上传路径
- **替换**: `CmsUtils` 和 `FileUploadController` 中的 `@Value`

#### EmailConfig
- **路径**: `src/main/java/com/xiaozhi/config/EmailConfig.java`
- **前缀**: `email`
- **管理配置**: SMTP 邮件服务配置
- **替换**: `UserController` 中的 `@Value`

#### VadConfig
- **路径**: `src/main/java/com/xiaozhi/config/VadConfig.java`
- **前缀**: `vad`
- **管理配置**: 语音活动检测相关配置
- **替换**: `VadService` 和 `SileroVadModel` 中的 `@Value`

#### McpConfig
- **路径**: `src/main/java/com/xiaozhi/config/McpConfig.java`
- **前缀**: `xiaozhi.mcp`
- **管理配置**: MCP 设备相关配置
- **替换**: `DeviceMcpService` 中的 `@Value`

### 2. 更新的现有配置类

#### MqttConfig
- **路径**: `src/main/java/com/xiaozhi/communication/server/mqtt/MqttConfig.java`
- **状态**: 已验证符合最佳实践

## 配置对比

### 原始 @Value 方式
```java
@Value("${server.port:8091}")
private int port;

@Value("${xiaozhi.upload-path:uploads}")
private String uploadPath;

@Value("${email.smtp.username}")
private String emailUsername;

@Value("${email.smtp.password}")
private String emailPassword;

@Value("${vad.prebuffer.ms:200}")
private int preBufferMs;

@Value("${vad.model.path:models/silero_vad.onnx}")
private String modelPath;

@Value("${xiaozhi.mcp:device:max.tools.count:32}")
private static int maxToolsCount = 32;
```

### 新的配置类方式
```java
@Resource
private ApplicationConfig applicationConfig;

@Resource
private EmailConfig emailConfig;

@Resource
private VadConfig vadConfig;

@Resource
private McpConfig mcpConfig;

// 使用方式
int port = applicationConfig.getServer().getPort();
String uploadPath = applicationConfig.getUpload().getPath();
String emailUsername = emailConfig.getSmtp().getUsername();
String emailPassword = emailConfig.getSmtp().getPassword();
int preBufferMs = vadConfig.getPrebufferMs();
String modelPath = vadConfig.getModel().getPath();
int maxToolsCount = mcpConfig.getDevice().getMaxToolsCount();
```

## YAML 配置更新

### 新增配置项
```yaml
# 小智应用配置
xiaozhi:
  server:
    port: 8091
  upload:
    path: uploads
  mcp:
    device:
      max-tools-count: 32
      timeout-ms: 30000
      auto-discovery: true
    connection:
      timeout-ms: 10000
      retry-count: 3
      retry-interval-ms: 1000

# VAD 配置
vad:
  prebuffer-ms: 200
  energy-threshold: 0.5
  speech-threshold: 0.5
  silence-threshold: 0.5
  silence-ms: 1000
  model:
    path: models/silero_vad.onnx
    type: silero
    sample-rate: 16000
    window-size: 512

# 邮件配置
email:
  smtp:
    username: ""
    password: ""
    host: smtp.gmail.com
    port: 587
    enable-tls: true
    enable-auth: true
```

## 重构的类

### 1. CmsUtils
- **变更**: 将 `@Value("${server.port:8091}")` 替换为注入 `ApplicationConfig`
- **使用**: `applicationConfig.getServer().getPort()`

### 2. FileUploadController
- **变更**: 将 `@Value("${xiaozhi.upload-path:uploads}")` 替换为注入 `ApplicationConfig`
- **使用**: `applicationConfig.getUpload().getPath()`

### 3. UserController
- **变更**: 将邮件相关 `@Value` 替换为注入 `EmailConfig`
- **使用**: `emailConfig.getSmtp().getUsername()` 和 `emailConfig.getSmtp().getPassword()`

### 4. VadService
- **变更**: 将 `@Value("${vad.prebuffer.ms:200}")` 替换为注入 `VadConfig`
- **使用**: `vadConfig.getPrebufferMs()`

### 5. SileroVadModel
- **变更**: 将 `@Value("${vad.model.path:models/silero_vad.onnx}")` 替换为注入 `VadConfig`
- **使用**: `vadConfig.getModel().getPath()`

### 6. DeviceMcpService
- **变更**: 将 `@Value("${xiaozhi.mcp:device:max.tools.count:32}")` 替换为注入 `McpConfig`
- **使用**: `mcpConfig.getDevice().getMaxToolsCount()`

## 验证测试

### 测试类
- **路径**: `src/test/com/xiaozhi/config/ConfigurationClassesTest.java`
- **功能**: 验证所有配置类正确加载和工作

### 验证结果
- ✅ 编译成功
- ✅ 应用启动成功（配置正确加载）
- ✅ 所有配置类正确注入
- ✅ 配置值正确读取

## 优势

### 1. 类型安全
- 编译时类型检查
- IDE 自动补全支持
- 减少运行时错误

### 2. 更好的组织
- 相关配置分组管理
- 层次化配置结构
- 更清晰的配置关系

### 3. 更好的维护性
- 集中配置管理
- 更容易添加新配置项
- 更好的文档化

### 4. 更好的测试支持
- 可以轻松模拟配置
- 更好的单元测试支持
- 配置验证更容易

## 最佳实践

1. **使用 @ConfigurationProperties**: 而不是 @Value
2. **分组相关配置**: 将相关配置放在同一个配置类中
3. **提供默认值**: 在配置类中设置合理的默认值
4. **使用嵌套类**: 为复杂配置提供层次结构
5. **添加验证**: 使用 Bean Validation 注解验证配置
6. **编写测试**: 为配置类编写单元测试

## 注意事项

1. **Spring Boot 版本**: 确保使用 Spring Boot 2.0+ 以获得最佳支持
2. **配置绑定**: 使用 `@EnableConfigurationProperties` 或 `@Component` 启用配置绑定
3. **配置前缀**: 选择有意义且不冲突的配置前缀
4. **向后兼容**: 保持配置键名的向后兼容性

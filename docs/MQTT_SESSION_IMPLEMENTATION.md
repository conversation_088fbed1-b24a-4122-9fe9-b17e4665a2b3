# MQTT Session 实现说明

## 概述

本项目新增了基于 EMQX 消息队列的 `MqttSession` 类，继承自 `ChatSession`，实现了基本的消息发送与接收功能。消息处理使用统一的 `MessageHandler`。

**重要更新**: 项目已从 Eclipse Paho MQTT 客户端迁移到 **HiveMQ MQTT 客户端**，提供更好的性能和现代化的异步 API。

## 架构设计

### MQTT 客户端库

**HiveMQ MQTT Client** (当前使用)
- **版本**: 1.3.3
- **优势**: 现代化设计、高性能、完全异步、类型安全
- **特点**: 支持 MQTT 3.1.1 和 MQTT 5.0、响应式编程、更好的错误处理
- **API**: 基于 CompletableFuture 的异步 API

### 核心组件

1. **MqttSession** - MQTT 会话实现类
   - 继承自 `ChatSession`
   - 使用 HiveMQ `Mqtt3AsyncClient` 实现异步通信
   - 支持文本消息、二进制消息、心跳消息等

2. **MqttMessageHandler** - MQTT 消息处理器
   - 监听 EMQX 消息队列
   - 直接创建和管理 MQTT 会话
   - 使用统一的 `SessionManager` 进行会话管理
   - 处理设备连接和断开

3. **MqttController** - REST API 控制器
   - 提供 MQTT 会话监控接口
   - 支持会话管理和测试功能
   - 直接使用 `SessionManager` 进行会话操作

5. **MqttConfig** - MQTT 配置类
   - 管理 MQTT 连接配置
   - 支持 Topic 构建和客户端配置

## 消息流程

### 设备连接流程

```mermaid
sequenceDiagram
    participant Device as 设备
    participant EMQX as EMQX Broker
    participant Handler as MqttMessageHandler
    participant Manager as MqttSessionManager
    participant Session as MqttSession

    Device->>EMQX: 发送 Hello 消息
    EMQX->>Handler: 转发 Hello 消息
    Handler->>Manager: 创建会话请求
    Manager->>Session: 创建 MqttSession
    Session->>EMQX: 订阅设备 Topic
    Session->>Device: 发送确认消息
```

### 消息处理流程

```mermaid
sequenceDiagram
    participant Device as 设备
    participant EMQX as EMQX Broker
    participant Session as MqttSession
    participant Handler as MessageHandler
    participant Service as 业务服务

    Device->>EMQX: 发送消息
    EMQX->>Session: 转发消息
    Session->>Handler: 处理消息
    Handler->>Service: 业务处理
    Service->>Handler: 返回结果
    Handler->>Session: 发送响应
    Session->>EMQX: 发布响应消息
    EMQX->>Device: 转发响应
```

## Topic 设计

### Topic 命名规范（混合设计）

- **前缀**: `xiaozhi`
- **设备到服务器**: `xiaozhi/device-server` （统一 Topic，所有设备共用）
- **服务器到设备**: `devices/p2p/{clientId}` （P2P Topic，每个客户端独立）

设备到服务器使用统一 Topic，设备标识通过消息内容中的 `deviceId` 字段区分。
服务器到设备使用 P2P Topic，每个客户端有独立的接收 Topic。

### 消息类型识别

与 WebSocket 版本保持一致，消息类型通过解析 JSON 消息的 `type` 字段确定，而不是通过不同的 Topic。

### 支持的消息类型

所有消息都通过统一的 Topic 传输，消息类型在 JSON 中指定：

1. **hello** - 设备连接消息
   ```json
   {
     "type": "hello",
     "transport": "mqtt",
     "deviceId": "device001",
     "audioParams": {...}
   }
   ```

2. **listen** - 监听控制消息
   ```json
   {
     "type": "listen",
     "state": "start",
     "mode": "auto"
   }
   ```

3. **iot** - IoT 设备消息
   ```json
   {
     "type": "iot",
     "descriptors": [...],
     "states": [...]
   }
   ```

4. **abort** - 中止消息
   ```json
   {
     "type": "abort",
     "reason": "user_interrupt"
   }
   ```

5. **goodbye** - 断开连接消息
   ```json
   {
     "type": "goodbye",
     "reason": "session_closed"
   }
   ```

6. **二进制消息** - 音频数据等
   - 直接发送二进制数据到 device-server Topic
   - 服务器通过数据格式判断为二进制消息

## 配置说明

### application.properties 配置

```properties
# MQTT Broker 配置 - 统一使用 device-server Topic 设计
mqtt.broker.host=localhost
mqtt.broker.port=1883
mqtt.broker.ssl=false
mqtt.broker.username=
mqtt.broker.password=
mqtt.broker.connection-timeout=30
mqtt.broker.keep-alive-interval=60
mqtt.broker.automatic-reconnect=true
mqtt.broker.clean-session=true

# MQTT 客户端配置
mqtt.client.id=xiaozhi-server
mqtt.client.random-suffix=true

# Topic 配置（统一 Topic 设计）
mqtt.topic.prefix=xiaozhi
mqtt.topic.qos=1

# 会话管理配置
mqtt.session.timeout=300
mqtt.session.heartbeat-interval=60
mqtt.session.cleanup-interval=30
```

## 使用示例

### 使用 MQTT 会话

```java
@Autowired
private SessionManager sessionManager;

// 通过设备ID获取会话
var session = sessionManager.getSessionByDeviceId("device001");
if (session instanceof MqttSession mqttSession) {
    // 发送文本消息
    mqttSession.sendTextMessage("{\"type\":\"tts\",\"content\":\"Hello World\"}");

    // 发送二进制消息
    byte[] audioData = getAudioData();
    mqttSession.sendBinaryMessage(audioData);
}

// 关闭会话
sessionManager.closeSession(session.getSessionId());
```

### 客户端使用方式

现在客户端需要：
1. 订阅 `devices/p2p/{clientId}` 接收服务器消息（P2P Topic）
2. 发布到 `xiaozhi/device-server` 发送消息给服务器（统一 Topic）
3. 在 JSON 消息中包含 `deviceId` 字段来标识设备
4. 所有消息类型通过 JSON 的 `type` 字段区分

### 消息流程示例

**设备连接流程**:
```
1. 设备发送到: xiaozhi/device-server
   {"type":"hello","transport":"mqtt","deviceId":"device001"}

2. 服务器解析消息，从 deviceId 字段识别设备，创建会话，获取客户端 clientId

3. 服务器响应到: devices/p2p/{clientId}
   {"type":"hello","transport":"mqtt","sessionId":"mqtt-device001-123456"}
```

**语音对话流程**:
```
1. 设备发送监听消息: xiaozhi/device-server
   {"type":"listen","state":"start","mode":"auto","deviceId":"device001"}

2. 设备发送音频数据: xiaozhi/device-server
   [二进制音频数据]

3. 服务器响应TTS消息: devices/p2p/{clientId}
   {"type":"tts","text":"你好","state":"start"}
```

### REST API 使用

```bash
# 获取会话统计
curl http://localhost:8091/api/mqtt/stats

# 获取所有会话
curl http://localhost:8091/api/mqtt/sessions

# 获取指定设备会话
curl http://localhost:8091/api/mqtt/session/device001

# 关闭设备会话
curl -X POST http://localhost:8091/api/mqtt/session/device001/close

# 发送测试消息
curl -X POST http://localhost:8091/api/mqtt/session/device001/test \
  -H "Content-Type: application/json" \
  -d '{"type":"text","message":"Hello Device"}'
```

## 部署要求

### EMQX 安装

1. **Docker 方式**:
```bash
docker run -d --name emqx -p 1883:1883 -p 8083:8083 -p 8084:8084 -p 8883:8883 -p 18083:18083 emqx/emqx:latest
```

2. **本地安装**:
```bash
# Ubuntu/Debian
sudo apt-get install emqx

# CentOS/RHEL
sudo yum install emqx

# 启动服务
sudo systemctl start emqx
```

### 配置验证

启动应用后，检查日志确认 MQTT 连接成功：

```
INFO  - MQTT 消息处理器已启动 - ClientId: xiaozhi-server-handler-1234567890
INFO  - MQTT 会话管理器已启动
INFO  - 订阅设备到服务器统一 Topic: xiaozhi/device-server
```

## 测试

### 单元测试

```bash
mvn test -Dtest=MqttSessionTest
```

### 集成测试

1. 启动 EMQX Broker
2. 启动应用
3. 使用 MQTT 客户端工具连接测试

### 客户端测试工具

推荐使用以下工具进行测试：
- **MQTT.fx** - GUI 客户端
- **mosquitto_pub/sub** - 命令行工具
- **MQTTX** - 现代化 MQTT 客户端

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 EMQX 是否运行
   - 验证网络连接和端口
   - 检查用户名密码配置

2. **消息丢失**
   - 检查 QoS 设置
   - 验证 Topic 订阅
   - 查看 EMQX 日志

3. **会话超时**
   - 调整心跳间隔
   - 检查网络稳定性
   - 增加会话超时时间

### 日志级别

在 `application.properties` 中设置日志级别：

```properties
logging.level.com.xiaozhi.communication.server.mqtt=DEBUG
```

## 扩展功能

### 支持的扩展

1. **消息加密** - 支持 AES-128-CTR 加密
2. **UDP 传输** - 支持 UDP 音频数据传输
3. **负载均衡** - 支持多实例部署
4. **监控告警** - 集成监控和告警系统

### 自定义开发

可以通过继承 `MqttSession` 或实现自定义的 `MessageHandler` 来扩展功能。

## 性能优化

### 建议配置

- **连接池**: 合理设置 MQTT 连接池大小
- **缓冲区**: 调整 UDP 缓冲区大小
- **线程池**: 配置合适的工作线程数
- **内存管理**: 及时清理不活跃会话

### 监控指标

- 活跃会话数量
- 消息吞吐量
- 连接成功率
- 平均响应时间

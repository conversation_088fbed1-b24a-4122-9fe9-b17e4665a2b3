# MCP Endpoint Controller

## 概述

`McpEndpointController` 是一个新增的控制器，用于查询 MCP (Model Context Protocol) 端点的可用工具。该控制器允许用户传入 MCP URL，并返回该端点提供的所有可用工具列表。

## API 端点

### 获取 MCP 端点可用工具

**端点**: `POST /api/v1/mcp/endpoint/tools`

**描述**: 传入 MCP URL，返回该端点的可用工具列表

**请求参数**:
- `mcpUrl` (query parameter, required): MCP 端点的 URL
- `headers` (request body, optional): 可选的 HTTP 头部信息，用于认证等目的

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/v1/mcp/endpoint/tools?mcpUrl=https://example.com/mcp" \
  -H "Content-Type: application/json" \
  -d '{
    "Authorization": "Bearer your-token",
    "X-Custom-Header": "custom-value"
  }'
```

**成功响应**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "endpoint": "https://example.com/mcp",
    "toolsCount": 3,
    "tools": [
      {
        "name": "search_web",
        "description": "Search the web for information",
        "inputSchema": {
          "type": "object",
          "properties": {
            "query": {
              "type": "string",
              "description": "Search query"
            }
          },
          "required": ["query"]
        }
      },
      {
        "name": "get_weather",
        "description": "Get current weather information",
        "inputSchema": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "Location to get weather for"
            }
          },
          "required": ["location"]
        }
      }
    ]
  }
}
```

**错误响应**:
```json
{
  "code": 400,
  "msg": "MCP URL不能为空"
}
```

或

```json
{
  "code": 500,
  "msg": "无法连接到MCP端点，请检查URL和网络连接"
}
```

## 功能特性

1. **URL 验证**: 验证输入的 MCP URL 是否为空
2. **连接初始化**: 按照 MCP 协议规范初始化连接
3. **工具列表获取**: 获取端点提供的所有可用工具
4. **错误处理**: 完善的错误处理机制，包括网络错误、协议错误等
5. **超时控制**: 设置 10 秒的请求超时时间
6. **自定义头部**: 支持传入自定义 HTTP 头部，用于认证等目的

## 实现细节

### MCP 协议支持

控制器实现了 MCP 协议的以下部分：

1. **初始化请求** (`initialize` method):
   - 协议版本: `2024-11-05`
   - 客户端信息: `xiaozhi-mcp-client v1.0.0`

2. **工具列表请求** (`tools/list` method):
   - 获取端点提供的所有工具
   - 包含工具名称、描述和输入模式

### 错误处理

- **连接错误**: 当无法连接到 MCP 端点时返回友好的错误信息
- **协议错误**: 当 MCP 端点返回错误时记录详细日志
- **超时错误**: 10 秒超时保护，避免长时间等待
- **参数验证**: 验证必需的参数是否提供

### 日志记录

控制器使用 SLF4J 进行日志记录，包括：
- 连接错误日志
- 协议错误日志
- 调试信息日志

## 使用场景

1. **工具发现**: 在连接到 MCP 端点之前，先查看该端点提供哪些工具
2. **调试和测试**: 验证 MCP 端点是否正常工作
3. **动态工具加载**: 根据端点提供的工具动态配置系统功能
4. **API 文档生成**: 基于工具信息生成 API 文档

## 与现有系统的集成

该控制器与现有的 MCP 系统集成：

- 使用 WebClient 进行 MCP JSON-RPC 通信，与现有 MCP 服务保持一致
- 遵循项目的 API 响应格式 (`Resp`)
- 使用统一的 `/api/v1` 前缀
- 支持 Swagger 文档生成
- 使用 `McpConfig` 进行配置管理

## 测试

控制器包含完整的单元测试：

- 空 URL 验证测试
- 无效 URL 处理测试
- 控制器初始化测试
- 错误处理测试

运行测试：
```bash
mvn test -Dtest=McpEndpointControllerTest
```

## 使用示例

### 基本使用

```bash
# 查询 MCP 端点的可用工具
curl -X POST "http://localhost:8091/api/v1/mcp/endpoint/tools?mcpUrl=https://your-mcp-server.com/mcp" \
  -H "Content-Type: application/json"
```

### 带认证头的使用

```bash
# 使用认证令牌查询工具
curl -X POST "http://localhost:8091/api/v1/mcp/endpoint/tools?mcpUrl=https://your-mcp-server.com/mcp" \
  -H "Content-Type: application/json" \
  -d '{
    "Authorization": "Bearer your-api-token",
    "X-API-Key": "your-api-key"
  }'
```

### JavaScript 示例

```javascript
// 使用 fetch API 调用
async function getMcpTools(mcpUrl, headers = {}) {
  const response = await fetch(`/api/v1/mcp/endpoint/tools?mcpUrl=${encodeURIComponent(mcpUrl)}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(headers)
  });

  const result = await response.json();

  if (result.code === 0) {
    console.log(`Found ${result.data.toolsCount} tools:`);
    result.data.tools.forEach(tool => {
      console.log(`- ${tool.name}: ${tool.description}`);
    });
    return result.data.tools;
  } else {
    console.error('Error:', result.msg);
    return null;
  }
}

// 使用示例
getMcpTools('https://your-mcp-server.com/mcp', {
  'Authorization': 'Bearer your-token'
});
```

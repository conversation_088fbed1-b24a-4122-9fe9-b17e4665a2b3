# STT 意图识别集成文档

## 概述

本文档描述了如何将意图识别功能集成到语音识别（STT）流程中，实现智能语音交互。

## 集成架构

### 原始流程
```
用户语音 → VAD检测 → STT识别 → LLM对话 → TTS合成 → 音频输出
```

### 集成后流程
```
用户语音 → VAD检测 → STT识别 → 意图识别 → 智能处理 → 音频输出
                                    ↓
                            ┌─────────────────┐
                            │   意图识别结果   │
                            └─────────────────┘
                                    ↓
                    ┌───────────────┼───────────────┐
                    ↓               ↓               ↓
              直接函数调用      LLM对话处理      错误回退
              (立即响应)      (增强理解)      (普通对话)
```

## 核心实现

### 1. DialogueService 修改

在 `startStt` 方法中，STT识别完成后调用意图识别：

```java
// 原来的处理方式
handleText(session, finalText, true);

// 修改后的处理方式
processWithIntentRecognition(session, finalText);
```

### 2. 意图识别处理逻辑

```java
private boolean processWithIntentRecognition(ChatSession session, String userText) {
    // 1. 进行意图识别
    String intentResult = intentService.recognize(userText);

    // 2. 根据结果类型进行不同处理
    if (intentResult.contains("意图识别错误")) {
        // 回退到普通对话，返回false表示需要继续聊天
        return false;
    } else if (intentResult.contains("continue_chat")) {
        // 继续普通聊天流程，返回false表示需要继续聊天
        return false;
    } else if (isDirectFunctionResult(intentResult)) {
        // 直接返回函数执行结果，返回true表示意图已处理
        Sentence resultSentence = new Sentence(1, intentResult, true, true);
        handleSentence(session, resultSentence);
        return true;
    } else {
        // 继续LLM处理，返回false表示需要继续聊天
        return false;
    }
}

// 调用方式
boolean intentHandled = processWithIntentRecognition(session, finalText);
if (!intentHandled) {
    handleText(session, finalText, true);
}
```

## 处理策略

### 1. 直接函数调用
当意图识别能够直接执行用户请求时：
- **场景**: "现在几点了？"、"声音调到80%"、"播放音乐"
- **处理**: 直接执行函数并返回结果，无需LLM参与
- **优势**: 响应速度快，准确性高

### 2. 继续聊天处理
当意图识别为普通对话时：
- **场景**: 复杂查询、多轮对话、需要上下文理解
- **标识**: 意图识别返回 `continue_chat`
- **处理**: 直接继续普通的LLM对话流程
- **优势**: 保持对话的自然性和灵活性

### 3. LLM增强处理
当需要更复杂的对话理解时：
- **场景**: 其他需要LLM处理的情况
- **处理**: 意图识别作为预处理，LLM进行深度理解
- **优势**: 保持对话的自然性和灵活性

### 4. 错误回退机制
当意图识别失败时：
- **场景**: 无法识别的意图、系统错误
- **处理**: 自动回退到原有的LLM对话模式
- **优势**: 确保系统的鲁棒性

## 支持的意图类型

### 1. 设备控制类
- **音量控制**: "声音调到80%" → 直接调用音量设置函数
- **播放控制**: "播放音乐" → 直接调用音乐播放函数
- **设备操作**: 各种设备相关的MCP工具调用

### 2. 信息查询类
- **时间查询**: "现在几点了？" → 调用时间查询函数
- **天气查询**: "今天天气怎么样？" → 调用天气API
- **其他查询**: 通过第三方MCP工具处理

### 3. 对话控制类
- **结束对话**: "结束对话"、"拜拜" → 直接结束会话
- **角色切换**: "切换到学习模式" → 调用角色切换函数

### 4. 普通对话意图
- **标识**: `continue_chat`
- **处理方式**: 直接继续LLM对话流程
- **用户体验**: 无缝的对话体验

### 5. 未知意图
- **处理方式**: 回退到LLM对话模式
- **用户体验**: 无缝切换，用户无感知

## 配置要求

### 1. 意图识别模型
确保在数据库中配置了意图识别模型：
```sql
INSERT INTO sys_config (name, type, model_type, provider, api_key, api_url, is_default) 
VALUES ('意图识别模型', 'llm', 'intent', 'openai', 'your-api-key', 'https://api.openai.com/v1', true);
```

### 2. 函数注册
确保相关函数已正确注册：
- 全局函数通过 `ToolsGlobalRegistry`
- 设备MCP工具通过 `DeviceMcpService`
- 第三方MCP工具通过 `ThirdPartyMcpService`

## 性能优化

### 1. 响应速度
- **直接函数调用**: 跳过LLM处理，显著提升响应速度
- **并行处理**: 意图识别与STT结果处理可以并行进行

### 2. 准确性提升
- **专门训练**: 意图识别模型专门针对常见意图训练
- **回退机制**: 确保复杂场景仍能正确处理

### 3. 资源使用
- **减少LLM调用**: 简单意图直接处理，减少LLM资源消耗
- **智能路由**: 根据意图复杂度选择处理方式

## 日志和监控

### 1. 关键日志
```java
logger.info("开始意图识别，用户输入: \"{}\"", userText);
logger.info("意图识别结果: {}", intentResult);
logger.info("意图识别返回直接结果，发送给用户: {}", intentResult);
```

### 2. 监控指标
- 意图识别成功率
- 直接函数调用比例
- 回退到LLM的比例
- 平均响应时间

## 使用示例

### 1. 音量控制
```
用户: "声音调到80%"
STT: "声音调到80%"
意图识别: {"function": {"name": "self.audio_speaker.set_volume", "arguments": {"volume": 80}}}
结果: "音量已调整到80%"
```

### 2. 时间查询
```
用户: "现在几点了？"
STT: "现在几点了？"
意图识别: {"function": {"name": "get_time"}}
结果: "现在是下午3点30分"
```

### 3. 普通对话
```
用户: "帮我分析一下今天的学习计划"
STT: "帮我分析一下今天的学习计划"
意图识别: {"function": {"name": "continue_chat"}}
处理: 继续普通LLM对话流程
LLM: 进行复杂的对话理解和回答
```

## 故障排除

### 1. 意图识别失败
- **检查模型配置**: 确保意图识别模型正确配置
- **查看日志**: 检查意图识别的详细错误信息
- **回退机制**: 系统会自动回退到LLM处理

### 2. 函数调用失败
- **函数注册**: 确保相关函数已正确注册
- **参数格式**: 检查函数参数格式是否正确
- **权限检查**: 确保有执行相关函数的权限

### 3. 性能问题
- **模型响应**: 检查意图识别模型的响应时间
- **并发处理**: 确保系统能够处理并发请求
- **资源监控**: 监控系统资源使用情况

## 未来扩展

### 1. 上下文感知
- 结合对话历史进行意图识别
- 支持多轮对话的意图理解

### 2. 学习优化
- 根据用户反馈优化意图识别
- 动态调整处理策略

### 3. 更多意图类型
- 支持更多设备控制意图
- 集成更多第三方服务

## 总结

通过将意图识别集成到STT流程中，系统能够：
1. **提升响应速度**: 简单意图直接处理
2. **保持灵活性**: 复杂场景仍使用LLM
3. **增强鲁棒性**: 完善的错误回退机制
4. **改善用户体验**: 更智能的语音交互

这种集成方式在保持系统原有功能的基础上，显著提升了语音交互的智能化水平。

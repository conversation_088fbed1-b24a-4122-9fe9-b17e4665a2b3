# MCP配置示例

## 数据库配置示例

### 1. 添加文件系统MCP端点
```sql
INSERT INTO `sys_mcp_endpoint` (
    `url`, 
    `name`, 
    `auth_token`, 
    `headers`, 
    `enabled`
) VALUES (
    'https://filesystem-mcp.example.com/api/v1/mcp',
    '文件系统MCP',
    'fs_token_12345',
    '{"Content-Type": "application/json", "X-Client-Version": "1.0"}',
    1
);
```

### 2. 添加Web搜索MCP端点
```sql
INSERT INTO `sys_mcp_endpoint` (
    `url`, 
    `name`, 
    `auth_token`, 
    `headers`, 
    `enabled`
) VALUES (
    'https://web-search-mcp.example.com/mcp',
    'Web搜索MCP',
    'search_api_key_67890',
    NULL,
    1
);
```

### 3. 添加数据库MCP端点（禁用状态）
```sql
INSERT INTO `sys_mcp_endpoint` (
    `url`, 
    `name`, 
    `auth_token`, 
    `headers`, 
    `enabled`
) VALUES (
    'https://database-mcp.example.com/mcp-endpoint',
    '数据库MCP',
    'db_access_token_abcdef',
    '{"X-Database-Type": "postgresql", "X-Max-Connections": "10"}',
    0  -- 禁用状态
);
```

## application.yml配置示例

```yaml
xiaozhi:
  mcp:
    device:
      max-tools-count: 32
      timeout-ms: 30000
      auto-discovery: true
    connection:
      timeout-ms: 10000
      retry-count: 3
      retry-interval-ms: 1000

# 日志配置
logging:
  level:
    com.xiaozhi.dialogue.llm.tool.mcp: DEBUG
    com.xiaozhi.communication.common.MessageHandler: DEBUG
```

## MCP端点响应示例

### 1. 初始化响应
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "tools": {}
    },
    "serverInfo": {
      "name": "filesystem-mcp",
      "version": "1.0.0"
    }
  }
}
```

### 2. 工具列表响应
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "result": {
    "tools": [
      {
        "name": "read_file",
        "description": "读取文件内容",
        "inputSchema": {
          "type": "object",
          "properties": {
            "path": {
              "type": "string",
              "description": "文件路径"
            }
          },
          "required": ["path"]
        }
      },
      {
        "name": "write_file",
        "description": "写入文件内容",
        "inputSchema": {
          "type": "object",
          "properties": {
            "path": {
              "type": "string",
              "description": "文件路径"
            },
            "content": {
              "type": "string",
              "description": "文件内容"
            }
          },
          "required": ["path", "content"]
        }
      },
      {
        "name": "list_directory",
        "description": "列出目录内容",
        "inputSchema": {
          "type": "object",
          "properties": {
            "path": {
              "type": "string",
              "description": "目录路径"
            }
          },
          "required": ["path"]
        }
      }
    ]
  }
}
```

### 3. 工具调用响应
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "文件内容读取成功：\n\nHello, World!\nThis is a test file."
      }
    ]
  }
}
```

## 使用场景示例

### 1. 聊天会话建立时的日志输出
```
2025-07-23 18:30:00.123 INFO  [MessageHandler] 开始查询设备信息 - DeviceId: device_123
2025-07-23 18:30:00.125 DEBUG [MessageHandler] 开始初始化MCP工具 - SessionId: session_456
2025-07-23 18:30:00.126 INFO  [McpToolRegistrationService] 开始初始化会话 session_456 的MCP工具
2025-07-23 18:30:00.127 DEBUG [McpToolRegistrationService] 初始化设备端MCP工具 - SessionId: session_456
2025-07-23 18:30:00.128 DEBUG [McpToolRegistrationService] 初始化第三方MCP工具 - SessionId: session_456
2025-07-23 18:30:00.129 INFO  [ThirdPartyMcpManager] 开始初始化启用的MCP端点...
2025-07-23 18:30:00.150 INFO  [ThirdPartyMcpManager] 成功连接到MCP端点: https://filesystem-mcp.example.com/api/v1/mcp, 获取到 3 个工具
2025-07-23 18:30:00.175 INFO  [ThirdPartyMcpManager] 成功连接到MCP端点: https://web-search-mcp.example.com/mcp, 获取到 2 个工具
2025-07-23 18:30:00.176 INFO  [ThirdPartyMcpManager] MCP端点初始化完成，成功连接 2/2 个端点
2025-07-23 18:30:00.177 INFO  [McpToolRegistrationService] 成功注册 5 个第三方MCP工具到会话 session_456
2025-07-23 18:30:00.200 DEBUG [McpToolRegistrationService] 设备端MCP工具初始化完成 - SessionId: session_456
2025-07-23 18:30:00.201 DEBUG [McpToolRegistrationService] 第三方MCP工具初始化完成 - SessionId: session_456
2025-07-23 18:30:00.202 INFO  [McpToolRegistrationService] 会话 session_456 的所有MCP工具初始化完成
2025-07-23 18:30:00.203 DEBUG [MessageHandler] MCP工具初始化完成 - SessionId: session_456
```

### 2. AI模型调用MCP工具
当AI模型需要读取文件时，会调用注册的 `mcp_read_file` 工具：

```json
{
  "tool_calls": [
    {
      "id": "call_123",
      "type": "function",
      "function": {
        "name": "mcp_read_file",
        "arguments": "{\"path\": \"/home/<USER>/document.txt\"}"
      }
    }
  ]
}
```

### 3. 工具统计信息
```java
McpToolRegistrationService.McpToolStats stats = mcpService.getMcpToolStats(chatSession);
System.out.println(stats.toString());
// 输出: McpToolStats{totalTools=15, mcpTools=8, thirdPartyMcpTools=5}
```

## 故障排除

### 1. MCP端点连接失败
```
2025-07-23 18:30:00.150 ERROR [ThirdPartyMcpService] Error initializing MCP connection: Connection refused
2025-07-23 18:30:00.151 ERROR [ThirdPartyMcpManager] 连接到MCP端点失败: https://offline-mcp.example.com/mcp
```

**解决方案：**
- 检查MCP端点URL是否正确
- 验证网络连接
- 检查认证token是否有效

### 2. 工具注册失败
```
2025-07-23 18:30:00.175 WARN  [ThirdPartyMcpService] Error getting tools from MCP endpoint https://broken-mcp.example.com/mcp: Invalid response format
```

**解决方案：**
- 检查MCP端点是否返回正确的JSON-RPC格式
- 验证工具定义是否符合MCP规范

### 3. 认证失败
```
2025-07-23 18:30:00.160 ERROR [ThirdPartyMcpService] Error calling tool read_file on MCP endpoint https://secure-mcp.example.com/mcp: 401 Unauthorized
```

**解决方案：**
- 检查数据库中的auth_token是否正确
- 验证HTTP headers配置
- 确认MCP端点的认证要求

## 性能优化建议

### 1. 连接池配置
```yaml
spring:
  webflux:
    client:
      pool:
        max-connections: 100
        max-idle-time: 30s
```

### 2. 超时配置调优
根据MCP端点的响应时间调整超时配置：
```yaml
xiaozhi:
  mcp:
    connection:
      timeout-ms: 5000  # 对于快速响应的端点
      # timeout-ms: 30000  # 对于慢速响应的端点
```

### 3. 工具数量限制
```yaml
xiaozhi:
  mcp:
    device:
      max-tools-count: 50  # 根据实际需求调整
```

# MCP Endpoint CRUD API 文档

## 概述

本文档描述了 MCP (Model Context Protocol) 端点管理的完整 CRUD API。该 API 允许用户管理 MCP 端点，包括添加、查询、更新、删除端点，以及获取端点的可用工具。

## 更新日志

### v2.0 (2025-07-23)
- ✅ 将 `getAvailableTools` 方法移动到 Service 层，提高代码复用性
- ✅ 修复 SSE 端点硬编码问题，现在可以从 URL 中智能提取 SSE 端点路径
- ✅ 支持自定义 SSE 端点路径，提高兼容性
- ✅ 统一错误处理和日志记录

## 基础信息

- **Base URL**: `/api/v1/mcp-endpoints`
- **认证**: 部分接口需要认证
- **响应格式**: 统一使用 `Resp` 格式

## API 端点

### 1. 获取端点列表

**GET** `/api/v1/mcp-endpoints`

**描述**: 分页查询 MCP 端点列表

**请求参数**:
- `pageNum` (query, optional): 页码，默认 1
- `pageSize` (query, optional): 每页大小，默认 10
- `name` (query, optional): 端点名称（模糊查询）
- `enabled` (query, optional): 是否启用（true/false）

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "示例MCP端点",
        "url": "https://api.example.com/mcp",
        "authToken": "token123",
        "headers": null,
        "isEnabled": true,
        "createdAt": "2025-07-23T10:00:00",
        "updatedAt": "2025-07-23T10:00:00"
      }
    ],
    "total": 1,
    "current": 1,
    "size": 10,
    "pages": 1
  }
}
```

### 2. 获取端点详情

**GET** `/api/v1/mcp-endpoints/{id}`

**描述**: 根据 ID 获取 MCP 端点详情

**路径参数**:
- `id` (Integer): 端点 ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "示例MCP端点",
    "url": "https://api.example.com/mcp",
    "authToken": "token123",
    "headers": null,
    "isEnabled": true,
    "createdAt": "2025-07-23T10:00:00",
    "updatedAt": "2025-07-23T10:00:00"
  }
}
```

### 3. 添加端点

**POST** `/api/v1/mcp-endpoints`

**描述**: 添加新的 MCP 端点

**请求体**:
```json
{
  "name": "新MCP端点",
  "url": "https://new-api.example.com/mcp",
  "authToken": "new-token123",
  "headers": {
    "X-Custom-Header": "custom-value"
  },
  "enabled": true
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 2,
    "name": "新MCP端点",
    "url": "https://new-api.example.com/mcp",
    "authToken": "new-token123",
    "headers": null,
    "isEnabled": true,
    "createdAt": "2025-07-23T11:00:00",
    "updatedAt": "2025-07-23T11:00:00"
  }
}
```

### 4. 更新端点

**PUT** `/api/v1/mcp-endpoints/{id}`

**描述**: 更新指定的 MCP 端点

**路径参数**:
- `id` (Integer): 端点 ID

**请求体**:
```json
{
  "name": "更新的MCP端点",
  "url": "https://updated-api.example.com/mcp",
  "authToken": "updated-token123",
  "headers": {
    "X-Updated-Header": "updated-value"
  },
  "enabled": false
}
```

### 5. 删除端点

**DELETE** `/api/v1/mcp-endpoints/{id}`

**描述**: 删除指定的 MCP 端点（软删除）

**路径参数**:
- `id` (Integer): 端点 ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": "删除成功"
}
```

### 6. 切换端点状态

**PUT** `/api/v1/mcp-endpoints/{id}/status`

**描述**: 启用或禁用 MCP 端点

**路径参数**:
- `id` (Integer): 端点 ID

**请求参数**:
- `enabled` (query, required): 是否启用（true/false）

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": "状态更新成功"
}
```

### 7. 测试端点连接

**POST** `/api/v1/mcp-endpoints/test-connection`

**描述**: 测试指定 MCP 端点的连接状态

**请求参数**:
- `url` (query, required): MCP 端点 URL
- `authToken` (query, optional): 认证令牌

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "connected": true,
    "toolsCount": 5,
    "serverInfo": {
      "name": "example-mcp-server",
      "version": "1.0.0"
    }
  }
}
```

### 8. 获取端点工具

**GET** `/api/v1/mcp-endpoints/{id}/tools`

**描述**: 获取已保存的 MCP 端点的可用工具

**路径参数**:
- `id` (Integer): 端点 ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "name": "search_web",
      "description": "Search the web for information",
      "inputSchema": {
        "type": "object",
        "properties": {
          "query": {
            "type": "string",
            "description": "Search query"
          }
        },
        "required": ["query"]
      }
    }
  ]
}
```

### 9. 获取任意端点工具（无需认证）

**POST** `/mcp-servers/tools`

**描述**: 传入任意 MCP URL，返回该端点的可用工具列表

**请求参数**:
- `mcpUrl` (query, required): MCP 端点 URL
- `mcpToken` (query, required): 认证令牌

**SSE 端点自动提取**:
系统会智能从 MCP URL 中提取 SSE 端点路径：
- 如果 URL 包含 `/sse` 或 `/mcp_server/sse`，将使用 URL 中的路径
- 否则使用默认路径 `/mcp_server/sse`
- 支持的 URL 格式：
  - `https://api.example.com/mcp` → SSE: `/mcp_server/sse`
  - `https://api.example.com/mcp/sse` → SSE: `/mcp/sse`
  - `https://api.example.com/custom/mcp_server/sse` → SSE: `/custom/mcp_server/sse`

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "name": "get_weather",
      "description": "Get current weather information",
      "inputSchema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "Location to get weather for"
          }
        },
        "required": ["location"]
      }
    }
  ]
}
```

## 错误响应

所有 API 在出错时都会返回统一的错误格式：

```json
{
  "code": 400,
  "msg": "错误描述信息"
}
```

常见错误码：
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 数据模型

### McpEndpointDto

```json
{
  "id": "Integer - 端点ID",
  "name": "String - 端点名称",
  "url": "String - 端点URL",
  "authToken": "String - 认证令牌",
  "headers": "Map<String, String> - 自定义头部",
  "enabled": "Boolean - 是否启用"
}
```

### SysMcpEndpoint

```json
{
  "id": "Integer - 端点ID",
  "name": "String - 端点名称", 
  "url": "String - 端点URL",
  "authToken": "String - 认证令牌",
  "headers": "String - 头部信息(JSON格式)",
  "isEnabled": "Boolean - 是否启用",
  "isDeleted": "Boolean - 是否删除",
  "createdAt": "Date - 创建时间",
  "updatedAt": "Date - 更新时间"
}
```

## 使用示例

### JavaScript 示例

```javascript
// 获取端点列表
const getEndpoints = async () => {
  const response = await fetch('/api/v1/mcp-endpoints?pageNum=1&pageSize=10');
  const result = await response.json();
  return result;
};

// 添加端点
const addEndpoint = async (endpoint) => {
  const response = await fetch('/api/v1/mcp-endpoints', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(endpoint)
  });
  const result = await response.json();
  return result;
};

// 测试连接
const testConnection = async (url, token) => {
  const response = await fetch(`/api/v1/mcp-endpoints/test-connection?url=${encodeURIComponent(url)}&authToken=${token}`, {
    method: 'POST'
  });
  const result = await response.json();
  return result;
};
```

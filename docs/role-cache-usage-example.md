# 角色缓存使用示例

## 基本使用

### 1. 获取当前角色

```java
@Service
public class ExampleService {
    
    @Resource
    private SessionManager sessionManager;
    
    public void processMessage(ChatSession session, String message) {
        // 优先从缓存获取角色，如果缓存中没有则查询数据库并缓存
        SysRole currentRole = sessionManager.getCurrentRole(session);
        
        if (currentRole != null) {
            System.out.println("当前角色: " + currentRole.getName());
            System.out.println("语音: " + currentRole.getVoice());
            
            // 使用角色信息进行后续处理...
        }
    }
}
```

### 2. 直接使用 ChatSession 方法

```java
public void anotherExample(ChatSession session) {
    // 检查是否已缓存角色
    if (session.hasCurrentRole()) {
        SysRole role = session.getCurrentRole();
        System.out.println("使用缓存的角色: " + role.getName());
    } else {
        // 需要通过 SessionManager 获取并缓存
        SysRole role = sessionManager.getCurrentRole(session);
        System.out.println("从数据库获取并缓存角色: " + role.getName());
    }
}
```

## 角色切换场景

### 1. 手动切换角色

```java
@Service
public class RoleSwitchService {
    
    @Resource
    private SessionManager sessionManager;
    
    @Resource
    private SysRoleService roleService;
    
    public boolean switchRole(String sessionId, Integer newRoleId) {
        try {
            // 获取新角色信息
            SysRole newRole = roleService.selectRoleById(newRoleId);
            if (newRole == null) {
                return false;
            }
            
            // 清除旧的角色缓存
            sessionManager.clearCurrentRole(sessionId);
            
            // 更新设备的角色ID（这里省略具体实现）
            // deviceService.updateRoleId(deviceId, newRoleId);
            
            // 设置新的角色缓存
            sessionManager.updateCurrentRole(sessionId, newRole);
            
            return true;
        } catch (Exception e) {
            logger.error("角色切换失败", e);
            return false;
        }
    }
}
```

### 2. 批量清除角色缓存

```java
@Service
public class RoleManagementService {
    
    @Resource
    private SessionManager sessionManager;
    
    /**
     * 角色更新后，清除所有使用该角色的会话缓存
     */
    public void onRoleUpdated(Integer roleId) {
        // 清除所有使用该角色的会话缓存
        sessionManager.clearCurrentRoleByRoleId(roleId);
        
        logger.info("已清除角色 {} 的所有会话缓存", roleId);
    }
}
```

## 性能对比示例

### 优化前的代码

```java
// 每次都查询数据库（虽然有 Spring Cache，但仍有开销）
public void processAudioData(ChatSession session, byte[] audioData) {
    SysDevice device = session.getSysDevice();
    SysRole role = roleService.selectRoleById(device.getRoleId()); // 数据库查询
    
    // 处理音频...
}

public void handleSentence(ChatSession session, String sentence) {
    SysDevice device = session.getSysDevice();
    SysRole role = roleService.selectRoleById(device.getRoleId()); // 又一次数据库查询
    
    // 处理句子...
}
```

### 优化后的代码

```java
// 使用会话级缓存，同一会话期间只查询一次数据库
public void processAudioData(ChatSession session, byte[] audioData) {
    SysRole role = sessionManager.getCurrentRole(session); // 首次查询数据库并缓存
    
    // 处理音频...
}

public void handleSentence(ChatSession session, String sentence) {
    SysRole role = sessionManager.getCurrentRole(session); // 使用缓存，无数据库查询
    
    // 处理句子...
}
```

## 最佳实践

### 1. 统一使用 SessionManager

```java
// ✅ 推荐：使用 SessionManager 获取角色
SysRole role = sessionManager.getCurrentRole(session);

// ❌ 不推荐：直接查询数据库
SysRole role = roleService.selectRoleById(device.getRoleId());
```

### 2. 角色更新时清除缓存

```java
@Service
public class RoleUpdateService {
    
    @Resource
    private SessionManager sessionManager;
    
    @Transactional
    public void updateRole(SysRole role) {
        // 更新数据库
        roleMapper.update(role);
        
        // 清除相关会话缓存
        sessionManager.clearCurrentRoleByRoleId(role.getId());
    }
}
```

### 3. 异常处理

```java
public void safeGetRole(ChatSession session) {
    try {
        SysRole role = sessionManager.getCurrentRole(session);
        if (role != null) {
            // 正常处理
        } else {
            // 角色不存在的处理
            logger.warn("会话 {} 的角色不存在", session.getSessionId());
        }
    } catch (Exception e) {
        // 异常处理
        logger.error("获取角色失败", e);
    }
}
```

## 监控和调试

### 1. 缓存命中率监控

```java
@Component
public class RoleCacheMonitor {
    
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    
    public void recordCacheHit() {
        cacheHits.incrementAndGet();
    }
    
    public void recordCacheMiss() {
        cacheMisses.incrementAndGet();
    }
    
    public double getCacheHitRate() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        return hits + misses == 0 ? 0.0 : (double) hits / (hits + misses);
    }
}
```

### 2. 调试日志

SessionManager 中已经包含了详细的调试日志：

```java
logger.debug("已缓存角色信息到会话 - sessionId: {}, roleId: {}, roleName: {}", 
    session.getSessionId(), role.getId(), role.getName());

logger.debug("已清除会话角色缓存 - sessionId: {}", sessionId);

logger.debug("已清除 {} 个会话的角色缓存 - roleId: {}", clearedCount, roleId);
```

启用调试日志：
```yaml
logging:
  level:
    com.xiaozhi.communication.common.SessionManager: DEBUG
```

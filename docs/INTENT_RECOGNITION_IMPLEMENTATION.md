# 意图识别服务实现文档

## 概述

`LlmIntentServiceImpl` 是一个基于大语言模型的意图识别服务实现，能够将用户的自然语言输入转换为结构化的函数调用。

## 功能特性

1. **LLM驱动的意图识别**: 使用配置的意图识别模型分析用户输入
2. **结构化JSON输出**: 返回标准化的函数调用格式
3. **多种函数调用支持**: 支持本地函数、设备MCP工具和第三方MCP工具
4. **错误处理**: 完善的异常处理和错误恢复机制
5. **参数提取**: 自动从用户输入中提取函数所需参数

## 使用方法

### 基本用法

```java
@Autowired
private IntentService intentService;

public void handleUserInput(String userText) {
    String result = intentService.recognize(userText);
    System.out.println("执行结果: " + result);
}
```

### 支持的输入示例

#### 1. 时间查询
```
用户输入: "现在几点了？"
LLM返回: {"function": {"name": "get_time"}}
执行结果: 调用时间查询函数
```

#### 2. 音量控制
```
用户输入: "声音调整到 80%"
LLM返回: {"function": {"name": "self.audio_speaker.set_volume", "arguments": {"volume": 80}}}
执行结果: 调用设备音量控制函数
```

#### 3. 音乐播放
```
用户输入: "播放音乐"
LLM返回: {"function": {"name": "func_playMusic", "arguments": {"songName": "随机音乐"}}}
执行结果: 调用音乐播放函数
```

#### 4. 结束对话
```
用户输入: "结束对话"
LLM返回: {"function": {"name": "func_stop_chat", "arguments": {"farewell": "再见！"}}}
执行结果: 结束当前对话会话
```

## 配置要求

### 1. 意图识别模型配置

需要在数据库中配置一个 `modelType` 为 `intent` 的模型配置：

```sql
INSERT INTO sys_config (name, type, model_type, provider, api_key, api_url, is_default) 
VALUES ('意图识别模型', 'llm', 'intent', 'openai', 'your-api-key', 'https://api.openai.com/v1', true);
```

### 2. 函数注册

确保相关函数已注册到系统中：

- **全局函数**: 通过 `ToolsGlobalRegistry` 注册
- **设备MCP工具**: 通过 `DeviceMcpService` 注册
- **第三方MCP工具**: 通过 `ThirdPartyMcpService` 注册

## 系统提示词

服务使用以下系统提示词指导LLM进行意图识别：

```
你是一个意图识别助手，需要分析用户输入并返回对应的函数调用。

你的任务是：
1. 理解用户的意图
2. 将意图映射到合适的函数调用
3. 提取函数所需的参数

返回格式要求：
- 必须返回纯JSON格式，不要包含任何其他文本
- 必须包含 function 字段
- function 必须包含 name 字段
- 如果函数需要参数，必须包含 arguments 字段

注意：
- 如果无法识别意图或没有合适的函数，返回: {"function": {"name": "unknown"}}
- 参数值要根据用户输入合理推断
- 音量、温度等数值参数要转换为数字类型
```

## 函数调用流程

1. **输入验证**: 检查用户输入是否为空
2. **LLM调用**: 使用意图识别模型分析用户输入
3. **JSON解析**: 解析LLM返回的JSON响应
4. **函数信息提取**: 提取函数名和参数
5. **函数调用**: 按优先级调用相应的函数：
   - 全局函数注册表
   - 设备MCP工具
   - 第三方MCP工具
6. **结果返回**: 返回函数执行结果

## 错误处理

### 常见错误类型

1. **输入为空**: 返回 "意图识别错误: 输入文本为空"
2. **LLM响应为空**: 返回 "意图识别错误: LLM返回空响应"
3. **JSON解析失败**: 返回 "意图识别错误: 无法解析LLM响应"
4. **函数信息缺失**: 返回 "意图识别错误: 无法提取函数信息"
5. **函数调用异常**: 返回具体的错误信息

### 未知意图处理

当LLM返回 `{"function": {"name": "unknown"}}` 时，系统会返回：
```
"抱歉，我无法理解您的意图。请尝试更清楚地表达您的需求。"
```

## 扩展指南

### 添加新的函数类型

1. 在 `callFunction` 方法中添加新的判断逻辑
2. 实现对应的调用方法
3. 更新系统提示词中的示例

### 自定义参数转换

修改 `convertArgumentsToStringMap` 方法以支持更复杂的参数类型转换。

### 增强错误处理

在各个处理步骤中添加更详细的错误信息和恢复策略。

## 测试

运行单元测试验证功能：

```bash
mvn test -Dtest=LlmIntentServiceImplTest
```

测试覆盖了以下场景：
- 空输入处理
- 有效意图识别
- 未知意图处理
- JSON解析错误
- LLM调用异常
- 模型配置缺失

## 注意事项

1. **模型配置**: 确保配置了合适的意图识别模型
2. **函数注册**: 确保所有需要的函数都已正确注册
3. **参数类型**: 注意参数类型转换，特别是数值类型
4. **性能考虑**: LLM调用可能较慢，考虑异步处理或缓存
5. **安全性**: 验证函数调用的安全性，避免恶意调用

## 未来改进

1. **缓存机制**: 对常见意图进行缓存以提高响应速度
2. **上下文感知**: 结合对话历史进行更准确的意图识别
3. **多轮对话**: 支持需要多轮交互的复杂意图
4. **置信度评估**: 返回意图识别的置信度分数
5. **自学习**: 根据用户反馈优化意图识别准确性

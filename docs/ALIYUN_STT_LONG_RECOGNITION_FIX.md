# 阿里云语音识别长时间识别支持修复

## 问题描述

AliyunSttService 中 streamRecognition 方法在5秒没人说话时会报错，无法支持长时间语音识别。

## 问题原因

原代码中存在以下问题：

1. **硬编码的5秒超时限制**：
   ```java
   .timeout(5, TimeUnit.SECONDS)
   ```

2. **未启用heartbeat机制**：没有配置阿里云语音识别的heartbeat参数来保持长连接

3. **缺少优化的识别参数**：未配置语言提示、断句方式等参数

## 解决方案

根据阿里云官方文档，在长时间静默的情况下，需要：

1. **启用heartbeat保持长连接**：将请求参数`heartbeat`设置为true
2. **移除固定超时限制**：让heartbeat机制处理长时间静默
3. **优化识别参数**：配置合适的语言提示和断句参数

## 修改内容

### 1. 启用heartbeat机制
```java
.parameter("heartbeat", true)
```

### 2. 添加语言提示
```java
.parameter("language_hints", new String[]{"zh", "en"})
```

### 3. 配置断句参数
```java
// 关闭语义断句，使用VAD断句以降低延迟
.parameter("semantic_punctuation_enabled", false)
// 设置VAD断句的静音时长阈值为1.5秒，适合对话场景
.parameter("max_sentence_silence", 1500)
```

### 4. 移除固定超时限制
移除了原来的 `.timeout(5, TimeUnit.SECONDS)` 调用

### 5. 改进错误处理
```java
error -> {
    logger.error("语音识别失败: {}", error.getMessage(), error);
    sink.error(error);
}
```

## 技术细节

### heartbeat机制工作原理

- 当启用heartbeat时，在持续发送静音音频的情况下，可保持与服务端的连接不中断
- 如果不启用heartbeat，即使持续发送静音音频，连接也将在60秒后因超时而断开

### VAD断句 vs 语义断句

- **VAD断句**：延迟较低，适合交互场景
- **语义断句**：准确性更高，适合会议转写场景

本次修改选择VAD断句以降低延迟，更适合实时对话场景。

### 静音时长阈值

设置为1500ms（1.5秒），当一段语音后的静音时长超过该阈值时，系统会判定该句子已结束。

## 参考文档

- [阿里云Paraformer实时语音识别Java SDK](https://help.aliyun.com/zh/model-studio/paraformer-real-time-speech-recognition-java-sdk)
- 官方FAQ：在长时间静默的情况下，如何保持与服务端长连接？

## 测试建议

建议测试以下场景：

1. **长时间静默**：测试超过5秒的静默期间是否会断开连接
2. **连续对话**：测试多轮对话是否正常工作
3. **混合场景**：测试说话和静默交替的场景

## 注意事项

1. 需要确保阿里云DashScope SDK版本不低于2.19.1（支持heartbeat参数）
2. heartbeat机制需要持续发送静音音频才能保持连接
3. 该修改主要针对实时对话场景优化，如需会议转写等场景可考虑启用语义断句

# ChatSession 角色缓存优化

## 概述

为了避免频繁查询数据库获取角色信息，在 `ChatSession` 类中添加了当前角色属性缓存机制。

## 问题背景

在原有实现中，以下场景会频繁调用 `roleService.selectRoleById()` 查询数据库：

1. **音频数据处理** - `DialogueService.processAudioData()` 每次处理音频时都查询角色
2. **句子处理** - `DialogueService.handleSentence()` 每个句子处理时都查询角色  
3. **聊天模型创建** - `ChatModelFactory.takeChatModel()` 每次创建模型时都查询角色
4. **消息处理初始化** - `MessageHandler` 在设备初始化时查询角色

虽然 `SysRoleService.selectRoleById()` 方法已经使用了 `@Cacheable` 注解进行缓存，但在高频场景下仍然存在性能开销。

## 解决方案

### 1. ChatSession 角色缓存

在 `ChatSession` 类中添加了 `currentRole` 属性：

```java
/**
 * 当前活跃角色（缓存，避免频繁查询数据库）
 */
protected SysRole currentRole;
```

提供了相关的管理方法：
- `getCurrentRole()` - 获取当前角色
- `setCurrentRole(SysRole role)` - 设置当前角色
- `clearCurrentRole()` - 清除角色缓存
- `hasCurrentRole()` - 检查是否已缓存角色

### 2. SessionManager 角色管理

在 `SessionManager` 中添加了统一的角色获取和管理方法：

```java
/**
 * 获取会话的当前角色（优先从缓存获取，避免频繁数据库查询）
 */
public SysRole getCurrentRole(ChatSession session) {
    // 优先从缓存获取
    if (session.hasCurrentRole()) {
        return session.getCurrentRole();
    }
    
    // 缓存中没有，从数据库查询并缓存
    SysRole role = roleService.selectRoleById(device.getRoleId());
    if (role != null) {
        session.setCurrentRole(role);
    }
    return role;
}
```

### 3. 更新现有代码

将以下代码中的直接数据库查询：
```java
SysRole role = roleService.selectRoleById(device.getRoleId());
```

替换为使用缓存的方式：
```java
SysRole role = sessionManager.getCurrentRole(session);
```

涉及的文件：
- `DialogueService.java` - 音频处理和句子处理
- `ChatModelFactory.java` - 聊天模型创建
- `MessageHandler.java` - 消息处理初始化

### 4. 缓存一致性保证

#### 角色切换时清除缓存
在 `ChangeRoleFunction` 中，角色切换时清除旧缓存并设置新缓存：

```java
// 清除角色缓存，确保下次获取最新角色信息
sessionManager.clearCurrentRole(chatSession.getSessionId());

// 更新角色缓存为新角色
sessionManager.updateCurrentRole(chatSession.getSessionId(), role);
```

#### 角色更新时清除相关会话缓存
在 `SysRoleServiceImpl` 中，角色更新/删除时清除所有使用该角色的会话缓存：

```java
// 清除所有使用该角色的会话缓存，确保下次获取最新角色信息
sessionManager.clearCurrentRoleByRoleId(role.getId());
```

## 性能优化效果

### 优化前
- 每次音频处理都查询数据库
- 每个句子处理都查询数据库
- 每次创建聊天模型都查询数据库
- 虽然有 Spring Cache，但仍有序列化/反序列化开销

### 优化后
- 会话期间角色信息缓存在内存中
- 只在首次获取或缓存失效时查询数据库
- 减少了 Spring Cache 的序列化/反序列化开销
- 提高了高频场景下的响应速度

## 缓存策略

1. **懒加载** - 只在首次需要时查询并缓存
2. **会话级缓存** - 缓存生命周期与会话一致
3. **主动失效** - 角色切换或角色更新时主动清除缓存
4. **容错处理** - 查询失败时不缓存，下次重新尝试

## 测试

创建了 `ChatSessionRoleCacheTest` 测试类，覆盖以下场景：
- 首次获取角色（查询数据库）
- 二次获取角色（使用缓存）
- 清除缓存功能
- 更新缓存功能
- 异常处理

## 注意事项

1. **内存使用** - 每个会话会缓存一个角色对象，内存占用很小
2. **数据一致性** - 通过主动清除缓存保证数据一致性
3. **向后兼容** - 不影响现有 API 和业务逻辑
4. **线程安全** - 会话对象本身是线程安全的，角色缓存操作也是安全的

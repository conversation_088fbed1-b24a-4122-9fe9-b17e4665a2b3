# 意图识别架构重构文档

## 重构概述

本次重构将意图识别和意图处理的职责进行了明确分离，提高了代码的可维护性和可扩展性。

## 架构变更

### 变更前 (旧架构)
```
用户输入 → IntentService.recognize() → 直接执行函数调用 → 返回执行结果
```

**问题**:
- `IntentService` 承担了太多职责（识别 + 执行）
- 函数调用逻辑分散在意图识别服务中
- 难以测试和维护
- 违反单一职责原则

### 变更后 (新架构)
```
用户输入 → IntentService.recognize() → 返回JSON格式意图
                                    ↓
DialogueService.processWithIntentRecognition() → 解析JSON → 执行函数调用 → 返回结果
```

**优势**:
- **职责分离**: `IntentService` 只负责意图识别，`DialogueService` 负责意图处理
- **统一接口**: 所有意图识别结果都是标准JSON格式
- **易于扩展**: 新增意图类型只需修改识别逻辑，处理逻辑统一
- **更好的测试性**: 可以独立测试意图识别和意图处理

## 详细变更

### 1. IntentService 变更

#### 变更前
```java
@Override
public String recognize(String text) {
    // 1. LLM意图识别
    // 2. 解析JSON结果
    // 3. 提取函数信息
    // 4. 执行函数调用 ❌ 职责过多
    String result = callFunction(functionName, functionInfo);
    return result;
}
```

#### 变更后
```java
@Override
public String recognize(String text) {
    // 1. LLM意图识别
    // 2. 解析JSON结果
    // 3. 验证JSON格式
    // 4. 返回标准JSON格式 ✅ 职责单一
    String intentJson = JsonUtil.toJson(intentResult);
    return intentJson;
}
```

**移除的方法**:
- `callFunction()` - 移至 DialogueService
- `callToolCallback()` - 移至 DialogueService  
- `callDeviceMcpTool()` - 移至 DialogueService
- `callThirdPartyMcpTool()` - 移至 DialogueService

**移除的依赖**:
- `ToolsGlobalRegistry`
- `DeviceMcpService`
- `ThirdPartyMcpService`
- `ToolCallback`
- `ToolContext`

### 2. DialogueService 变更

#### 新增方法
```java
// JSON解析
private Map<String, Object> parseIntentJson(String intentResult)
private Map<String, Object> extractFunctionInfo(Map<String, Object> intentJson)

// 函数执行
private String executeFunction(ChatSession session, String functionName, Map<String, Object> functionInfo)
private String callToolCallback(ToolCallback toolCallback, Map<String, Object> arguments, ChatSession session)
private String callDeviceMcpTool(ChatSession session, String functionName, Map<String, Object> arguments)
private String callThirdPartyMcpTool(String functionName, Map<String, Object> arguments)
```

#### 新增依赖注入
```java
@Resource
private ToolsGlobalRegistry toolsGlobalRegistry;

@Resource
private DeviceMcpService deviceMcpService;

@Resource
private ThirdPartyMcpService thirdPartyMcpService;
```

### 3. 处理流程变更

#### 变更前
```java
private boolean processWithIntentRecognition(ChatSession session, String userText) {
    String intentResult = intentService.recognize(userText); // 直接返回执行结果
    
    if (isDirectFunctionResult(intentResult)) {
        // 直接发送结果
        handleSentence(session, new Sentence(1, intentResult, true, true));
        return true;
    }
    return false;
}
```

#### 变更后
```java
private boolean processWithIntentRecognition(ChatSession session, String userText) {
    String intentResult = intentService.recognize(userText); // 返回JSON格式
    
    // 解析JSON
    Map<String, Object> intentJson = parseIntentJson(intentResult);
    Map<String, Object> functionInfo = extractFunctionInfo(intentJson);
    String functionName = (String) functionInfo.get("name");
    
    if ("continue_chat".equals(functionName)) {
        return false;
    }
    
    // 执行函数调用
    String functionResult = executeFunction(session, functionName, functionInfo);
    if (functionResult != null) {
        handleSentence(session, new Sentence(1, functionResult, true, true));
        return true;
    }
    return false;
}
```

## 数据流变更

### 意图识别结果格式

#### 标准成功格式
```json
{
  "function": {
    "name": "self.audio_speaker.set_volume",
    "arguments": {
      "volume": 80
    }
  }
}
```

#### continue_chat格式
```json
{
  "function": {
    "name": "continue_chat"
  }
}
```

#### 错误格式
```
"意图识别错误: 具体错误信息"
```

### 函数执行流程

1. **全局函数注册表查找**
   ```java
   ToolCallback toolCallback = toolsGlobalRegistry.resolve(functionName);
   if (toolCallback != null) {
       return callToolCallback(toolCallback, arguments, session);
   }
   ```

2. **设备MCP工具调用**
   ```java
   if (functionName.startsWith("self.") || functionName.contains("device")) {
       return callDeviceMcpTool(session, functionName, arguments);
   }
   ```

3. **第三方MCP工具调用**
   ```java
   return callThirdPartyMcpTool(functionName, arguments);
   ```

## 测试变更

### IntentService 测试
- **专注于意图识别**: 只测试JSON格式的返回结果
- **移除函数执行测试**: 不再测试函数调用逻辑

### DialogueService 测试
- **新增JSON解析测试**: 测试各种JSON格式的解析
- **新增函数执行测试**: 测试不同类型的函数调用
- **更新集成测试**: 测试完整的意图识别→处理流程

## 配置变更

### 依赖注入变更
- `IntentService`: 移除工具相关依赖
- `DialogueService`: 新增工具相关依赖

### 导入变更
```java
// IntentService 移除
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import com.xiaozhi.dialogue.llm.tool.mcp.device.DeviceMcpService;
import com.xiaozhi.dialogue.llm.tool.mcp.thirdparty.ThirdPartyMcpService;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;

// DialogueService 新增
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import com.xiaozhi.dialogue.llm.tool.mcp.device.DeviceMcpService;
import com.xiaozhi.dialogue.llm.tool.mcp.thirdparty.ThirdPartyMcpService;
import com.xiaozhi.utils.JsonUtil;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
```

## 优势总结

### 1. 职责分离
- **IntentService**: 专注于意图识别，返回标准化JSON结果
- **DialogueService**: 专注于意图处理和函数执行

### 2. 可维护性提升
- 意图识别逻辑集中在一个服务中
- 函数执行逻辑集中在对话服务中
- 更清晰的代码结构和依赖关系

### 3. 可扩展性增强
- 新增意图类型只需修改意图识别逻辑
- 新增函数类型只需修改函数执行逻辑
- 两者可以独立演进

### 4. 测试性改善
- 可以独立测试意图识别的准确性
- 可以独立测试函数执行的正确性
- 更容易进行单元测试和集成测试

### 5. 错误处理统一
- 意图识别错误在IntentService中统一处理
- 函数执行错误在DialogueService中统一处理
- 更清晰的错误边界和处理策略

## 迁移指南

### 对于开发者
1. **意图识别**: 只需关注LLM返回的JSON格式是否正确
2. **函数开发**: 在DialogueService中添加新的函数执行逻辑
3. **测试**: 分别测试意图识别和函数执行的逻辑

### 对于系统集成
1. **配置检查**: 确保依赖注入配置正确
2. **日志监控**: 关注意图识别和函数执行的分别日志
3. **性能监控**: 分别监控两个阶段的性能指标

这次重构显著提升了系统的架构质量，为后续的功能扩展和维护奠定了良好的基础。

FROM registry-vpc.cn-beijing.aliyuncs.com/pangtech/maven:3.9.6-eclipse-temurin-21-alpine as builder

WORKDIR /app

COPY pom.xml ./

COPY src ./src

COPY models ./models

RUN mvn -B -s /usr/share/maven/ref/settings-docker.xml clean package



FROM eclipse-temurin:21-jre

ARG APP_NAME
ARG ACTIVE_ENV
ENV ACTIVE_ENV=$ACTIVE_ENV

RUN apt-get update && apt-get install -y ffmpeg

# RUN addgroup -S spring && adduser -S spring -G spring

# USER spring:spring

WORKDIR /app

COPY --from=builder /app/target/${APP_NAME}-0.0.1-SNAPSHOT.jar app.jar

COPY --from=builder /app/models models

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "--enable-preview", "-Dspring.profiles.active=${ACTIVE_ENV}", "app.jar"]
#!/bin/bash

# Properties 文件清理脚本
# 删除原始的 properties 文件（在确认 YAML 配置正常工作后）

RESOURCES_DIR="src/main/resources"

echo "🧹 开始清理原始 Properties 配置文件..."

# 确认用户是否要继续
echo "⚠️  此操作将删除以下文件:"
[ -f "$RESOURCES_DIR/application.properties" ] && echo "   - application.properties"
[ -f "$RESOURCES_DIR/application-dev.properties" ] && echo "   - application-dev.properties"
[ -f "$RESOURCES_DIR/application-prod.properties" ] && echo "   - application-prod.properties"

echo ""
echo "📋 请确认:"
echo "1. 已经运行过 ./scripts/backup-properties.sh 创建备份"
echo "2. 已经运行过 ./scripts/verify-yaml-config.sh 验证配置"
echo "3. YAML 配置文件工作正常"

read -p "确认删除原始 properties 文件? (y/N): " -n 1 -r
echo

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 1
fi

# 删除 properties 文件
DELETED_COUNT=0

if [ -f "$RESOURCES_DIR/application.properties" ]; then
    rm "$RESOURCES_DIR/application.properties"
    echo "🗑️  已删除: application.properties"
    ((DELETED_COUNT++))
fi

if [ -f "$RESOURCES_DIR/application-dev.properties" ]; then
    rm "$RESOURCES_DIR/application-dev.properties"
    echo "🗑️  已删除: application-dev.properties"
    ((DELETED_COUNT++))
fi

if [ -f "$RESOURCES_DIR/application-prod.properties" ]; then
    rm "$RESOURCES_DIR/application-prod.properties"
    echo "🗑️  已删除: application-prod.properties"
    ((DELETED_COUNT++))
fi

if [ $DELETED_COUNT -eq 0 ]; then
    echo "ℹ️  没有找到需要删除的 properties 文件"
else
    echo "✅ 已删除 $DELETED_COUNT 个 properties 文件"
fi

echo ""
echo "🎉 清理完成！"
echo ""
echo "📁 当前配置文件:"
ls -la "$RESOURCES_DIR"/*.yml 2>/dev/null || echo "未找到 YAML 配置文件"

echo ""
echo "💡 提示:"
echo "- 如需恢复，请使用备份目录中的文件"
echo "- 备份文件位置: backup/properties-*/"

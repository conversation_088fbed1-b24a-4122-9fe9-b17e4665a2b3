#!/bin/bash

# MQTT+UDP ChatSession 测试脚本

echo "=== MQTT+UDP ChatSession 系统测试 ==="

# 检查Java版本
echo "1. 检查Java版本..."
java -version

# 编译项目
echo "2. 编译项目..."
mvn compile -q
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi
echo "✅ 编译成功"

# 检查关键类是否存在
echo "3. 检查关键类..."
CLASSES=(
    "target/classes/com/xiaozhi/communication/server/mqtt/MqttConfig.class"
    "target/classes/com/xiaozhi/communication/server/mqtt/MqttSession.class"
    "target/classes/com/xiaozhi/communication/server/mqtt/MqttMessageHandler.class"
    "target/classes/com/xiaozhi/communication/server/mqtt/MqttIntegrationConfig.class"
    "target/classes/com/xiaozhi/communication/server/mqtt/udp/UdpServer.class"
    "target/classes/com/xiaozhi/communication/server/mqtt/udp/UdpSessionManager.class"
    "target/classes/com/xiaozhi/communication/server/mqtt/crypto/CryptoUtils.class"
)

for class_file in "${CLASSES[@]}"; do
    if [ -f "$class_file" ]; then
        echo "✅ $(basename $class_file)"
    else
        echo "❌ $(basename $class_file) 不存在"
        exit 1
    fi
done

# 检查配置文件
echo "4. 检查配置文件..."
if [ -f "src/main/resources/application.yml" ]; then
    echo "✅ application.yml 存在"

    # 检查MQTT+UDP配置
    if grep -q "port: 8884" src/main/resources/application.yml; then
        echo "✅ UDP配置存在"
    else
        echo "❌ UDP配置缺失"
        exit 1
    fi

    if grep -q "algorithm: AES-128-CTR" src/main/resources/application.yml; then
        echo "✅ 加密配置存在"
    else
        echo "❌ 加密配置缺失"
        exit 1
    fi
elif [ -f "src/main/resources/application.properties" ]; then
    echo "✅ application.properties 存在（旧格式）"

    # 检查MQTT+UDP配置
    if grep -q "mqtt.udp.port" src/main/resources/application.properties; then
        echo "✅ UDP配置存在"
    else
        echo "❌ UDP配置缺失"
        exit 1
    fi

    if grep -q "mqtt.encryption" src/main/resources/application.properties; then
        echo "✅ 加密配置存在"
    else
        echo "❌ 加密配置缺失"
        exit 1
    fi
else
    echo "❌ 配置文件不存在（application.yml 或 application.properties）"
    exit 1
fi

# 检查文档
echo "5. 检查文档..."
if [ -f "docs/MQTT_UDP_USAGE.md" ]; then
    echo "✅ MQTT+UDP使用文档存在"
else
    echo "❌ MQTT+UDP使用文档缺失"
fi

# 尝试启动应用（快速验证）
echo "6. 快速启动验证..."
timeout 10s mvn spring-boot:run -Dspring-boot.run.arguments="--server.port=0" > /dev/null 2>&1 &
PID=$!
sleep 5

if ps -p $PID > /dev/null; then
    echo "✅ 应用可以正常启动"
    kill $PID 2>/dev/null
else
    echo "⚠️  应用启动可能有问题（需要MQTT Broker）"
fi

echo ""
echo "=== 测试总结 ==="
echo "✅ MQTT+UDP ChatSession 系统实现完成"
echo "✅ 所有核心组件编译成功"
echo "✅ 配置文件完整"
echo ""
echo "📋 系统组件："
echo "   - MqttConfig: MQTT和UDP配置管理"
echo "   - MqttSession: MQTT会话实现"
echo "   - MqttMessageHandler: MQTT消息处理"
echo "   - UdpServer: UDP音频数据接收"
echo "   - UdpSessionManager: UDP会话管理"
echo "   - CryptoUtils: AES-128-CTR加密"
echo ""
echo "🚀 启动说明："
echo "   1. 确保MQTT Broker运行在端口1883"
echo "   2. 运行: mvn spring-boot:run"
echo "   3. MQTT服务: localhost:1883"
echo "   4. UDP服务: localhost:8884"
echo "   5. 监控API: http://localhost:8091/api/mqtt/stats"
echo ""
echo "📖 详细文档: docs/MQTT_UDP_USAGE.md"

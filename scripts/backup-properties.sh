#!/bin/bash

# Properties 文件备份脚本
# 在删除原始 properties 文件之前创建备份

BACKUP_DIR="backup/properties-$(date +%Y%m%d-%H%M%S)"
RESOURCES_DIR="src/main/resources"

echo "🔄 开始备份 Properties 配置文件..."

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 备份 properties 文件
if [ -f "$RESOURCES_DIR/application.properties" ]; then
    cp "$RESOURCES_DIR/application.properties" "$BACKUP_DIR/"
    echo "✅ 已备份: application.properties"
else
    echo "⚠️  未找到: application.properties"
fi

if [ -f "$RESOURCES_DIR/application-dev.properties" ]; then
    cp "$RESOURCES_DIR/application-dev.properties" "$BACKUP_DIR/"
    echo "✅ 已备份: application-dev.properties"
else
    echo "⚠️  未找到: application-dev.properties"
fi

if [ -f "$RESOURCES_DIR/application-prod.properties" ]; then
    cp "$RESOURCES_DIR/application-prod.properties" "$BACKUP_DIR/"
    echo "✅ 已备份: application-prod.properties"
else
    echo "ℹ️  未找到: application-prod.properties (可能不存在)"
fi

echo "📁 备份文件保存在: $BACKUP_DIR"
echo "✨ 备份完成！"

# 显示备份内容
echo ""
echo "📋 备份文件列表:"
ls -la "$BACKUP_DIR"

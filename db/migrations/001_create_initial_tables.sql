-- =====================================================
-- 数据库迁移脚本 - 001 创建初始表结构
-- 文件名: 001_create_initial_tables.sql
-- 创建时间: 2025-01-10
-- 描述: 创建用户、角色、设备等核心表
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `xiaozhi` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `xiaozhi`;

-- =====================================================
-- 用户表 (sys_user)
-- =====================================================
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `user_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓名',
  `tel` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像',
  `status` enum('ACTIVE','INACTIVE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'ACTIVE' COMMENT '用户状态：ACTIVE-正常，INACTIVE-禁用',
  `is_admin` tinyint(1) DEFAULT 0 COMMENT '是否管理员：0-普通用户，1-管理员',
  `login_ip` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上次登录IP',
  `login_time` datetime DEFAULT NULL COMMENT '上次登录时间',
  `total_message` int DEFAULT 0 COMMENT '对话次数',
  `alive_number` int DEFAULT 0 COMMENT '在线设备数',
  `total_device` int DEFAULT 0 COMMENT '总设备数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =====================================================
-- 角色表 (sys_role)
-- =====================================================
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `role_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '角色ID，主键',
  `user_id` int unsigned NOT NULL COMMENT '用户ID，角色所属用户',
  `role_name` varchar(100) NOT NULL COMMENT '角色名称',
  `role_desc` TEXT DEFAULT NULL COMMENT '角色描述',
  `avatar` varchar(255) DEFAULT NULL COMMENT '角色头像',
  `voice_name` varchar(100) NOT NULL COMMENT '角色语音名称',
  `status` enum('ACTIVE','INACTIVE') DEFAULT 'ACTIVE' COMMENT '角色状态',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认角色',
  `model_id` int unsigned DEFAULT NULL COMMENT '模型ID',
  `model_name` varchar(100) DEFAULT NULL COMMENT '模型名称',
  `model_provider` varchar(50) DEFAULT NULL COMMENT '模型提供商',
  `temperature` decimal(3,2) DEFAULT 0.70 COMMENT '温度参数',
  `top_p` decimal(3,2) DEFAULT 0.90 COMMENT 'TopP参数',
  `stt_id` int unsigned DEFAULT NULL COMMENT 'STT服务ID',
  `tts_id` int DEFAULT NULL COMMENT 'TTS服务ID',
  `tts_provider` varchar(50) DEFAULT NULL COMMENT 'TTS提供商',
  `vad_speech_threshold` float DEFAULT 0.5 COMMENT '语音检测阈值',
  `vad_silence_threshold` float DEFAULT 0.3 COMMENT '静音检测阈值',
  `vad_energy_threshold` float DEFAULT 0.01 COMMENT '能量检测阈值',
  `vad_silence_ms` int DEFAULT 1200 COMMENT '静音检测时间(毫秒)',
  `total_device` int DEFAULT 0 COMMENT '使用此角色的设备数量',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_name` (`role_name`),
  KEY `idx_status` (`status`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_role_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色配置表';

-- =====================================================
-- 设备表 (sys_device)
-- =====================================================
DROP TABLE IF EXISTS `sys_device`;
CREATE TABLE `sys_device` (
  `device_id` varchar(255) NOT NULL COMMENT '设备ID，主键',
  `user_id` int unsigned NOT NULL COMMENT '用户ID，设备所属用户',
  `role_id` int unsigned DEFAULT NULL COMMENT '角色ID，设备关联的角色',
  `device_name` varchar(100) NOT NULL COMMENT '设备名称',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `status` enum('ONLINE','OFFLINE') DEFAULT 'OFFLINE' COMMENT '设备状态：ONLINE-在线，OFFLINE-离线',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `wifi_name` varchar(100) DEFAULT NULL COMMENT 'WiFi名称',
  `chip_model_name` varchar(100) DEFAULT NULL COMMENT '芯片型号',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `firmware_version` varchar(50) DEFAULT NULL COMMENT '固件版本',
  `function_names` varchar(250) DEFAULT NULL COMMENT '可用全局function的名称列表(逗号分割)，为空则使用所有全局function',
  `total_message` int DEFAULT 0 COMMENT '设备对话次数',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后在线时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`device_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_device_name` (`device_name`),
  KEY `idx_status` (`status`),
  KEY `idx_last_login_at` (`last_login_at`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_device_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_device_role` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`role_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备表';

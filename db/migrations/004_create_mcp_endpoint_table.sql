-- =====================================================
-- 数据库迁移脚本 - 004 创建MCP端点表
-- 文件名: 004_create_mcp_endpoint_table.sql
-- 创建时间: 2025-07-23
-- 描述: 创建用于存储第三方MCP端点信息的表
-- =====================================================

USE `xiaozhi`;

-- =====================================================
-- 创建MCP端点表
-- =====================================================

CREATE TABLE IF NOT EXISTS `sys_mcp_endpoint` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `url` VARCHAR(512) NOT NULL COMMENT 'MCP端点URL',
  `name` VARCHAR(255) NOT NULL COMMENT '端点名称',
  `auth_token` VARCHAR(512) DEFAULT NULL COMMENT '认证令牌',
  `headers` TEXT DEFAULT NULL COMMENT '其他HTTP头信息（JSON格式）',
  `enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用（0:禁用,1:启用）',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标志（0:未删除,1:已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_url` (`url`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MCP端点表';

-- =====================================================
-- 插入默认数据（可选）
-- =====================================================

-- INSERT INTO `sys_mcp_endpoint` (`url`, `name`, `enabled`) VALUES 
-- ('https://api.example.com/mcp', '示例MCP端点', 1);

-- =====================================================
-- 表结构说明
-- =====================================================

/*
表字段说明：

- id: 主键，自增ID
- url: MCP端点的URL地址，必须唯一
- name: MCP端点的名称，用于标识
- auth_token: 认证令牌，用于需要认证的端点
- headers: 其他HTTP头信息，以JSON格式存储
- enabled: 端点是否启用，0表示禁用，1表示启用
- create_time: 记录创建时间
- update_time: 记录更新时间
- del_flag: 软删除标志，0表示未删除，1表示已删除

索引说明：

- 主键索引: id
- 唯一索引: uk_url (url)
- 普通索引: idx_enabled (enabled)
- 普通索引: idx_create_time (create_time)
*/
-- =====================================================
-- 数据库迁移脚本 - 003 创建索引和约束
-- 文件名: 003_create_indexes_and_constraints.sql
-- 创建时间: 2025-01-10
-- 描述: 创建额外的索引和约束以优化性能
-- =====================================================

USE `xiaozhi`;

-- =====================================================
-- 复合索引优化
-- =====================================================

-- 用户表复合索引
ALTER TABLE `sys_user` 
ADD INDEX `idx_status_admin` (`status`, `is_admin`),
ADD INDEX `idx_login_time_status` (`login_time`, `status`);

-- 角色表复合索引
ALTER TABLE `sys_role` 
ADD INDEX `idx_user_status` (`user_id`, `status`),
ADD INDEX `idx_default_status` (`is_default`, `status`),
ADD INDEX `idx_provider_model` (`model_provider`, `model_id`);

-- 设备表复合索引
ALTER TABLE `sys_device` 
ADD INDEX `idx_user_status` (`user_id`, `status`),
ADD INDEX `idx_role_status` (`role_id`, `status`),
ADD INDEX `idx_status_last_login` (`status`, `last_login_at`),
ADD INDEX `idx_user_device_name` (`user_id`, `device_name`);

-- 消息表复合索引
ALTER TABLE `sys_message` 
ADD INDEX `idx_device_session` (`device_id`, `session_id`),
ADD INDEX `idx_user_created` (`user_id`, `created_at`),
ADD INDEX `idx_device_created` (`device_id`, `created_at`),
ADD INDEX `idx_session_created` (`session_id`, `created_at`),
ADD INDEX `idx_type_status` (`message_type`, `status`);

-- 验证码表复合索引
ALTER TABLE `sys_verification_code` 
ADD INDEX `idx_email_used` (`email`, `is_used`),
ADD INDEX `idx_device_used` (`device_id`, `is_used`),
ADD INDEX `idx_expires_used` (`expires_at`, `is_used`);

-- 模板表复合索引
ALTER TABLE `sys_template` 
ADD INDEX `idx_user_type` (`user_id`, `template_type`),
ADD INDEX `idx_public_status` (`is_public`, `status`),
ADD INDEX `idx_category_status` (`category`, `status`),
ADD INDEX `idx_type_category` (`template_type`, `category`);

-- =====================================================
-- 全文索引（用于搜索功能）
-- =====================================================

-- 角色表全文索引
ALTER TABLE `sys_role` 
ADD FULLTEXT INDEX `ft_role_search` (`role_name`, `role_desc`);

-- 设备表全文索引
ALTER TABLE `sys_device` 
ADD FULLTEXT INDEX `ft_device_search` (`device_name`, `chip_model_name`);

-- 模板表全文索引
ALTER TABLE `sys_template` 
ADD FULLTEXT INDEX `ft_template_search` (`template_name`, `template_desc`, `tags`);

-- 消息表全文索引（仅对文本内容）
ALTER TABLE `sys_message` 
ADD FULLTEXT INDEX `ft_message_content` (`message_content`);

-- =====================================================
-- 检查约束（MySQL 8.0+）
-- =====================================================

-- 用户表检查约束
ALTER TABLE `sys_user` 
ADD CONSTRAINT `chk_user_email_format` CHECK (email IS NULL OR email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'),
ADD CONSTRAINT `chk_user_tel_format` CHECK (tel IS NULL OR tel REGEXP '^[0-9+\\-\\s()]{10,20}$');

-- 角色表检查约束
ALTER TABLE `sys_role` 
ADD CONSTRAINT `chk_role_temperature` CHECK (temperature >= 0.0 AND temperature <= 2.0),
ADD CONSTRAINT `chk_role_top_p` CHECK (top_p >= 0.0 AND top_p <= 1.0),
ADD CONSTRAINT `chk_role_vad_thresholds` CHECK (
  vad_speech_threshold >= 0.0 AND vad_speech_threshold <= 1.0 AND
  vad_silence_threshold >= 0.0 AND vad_silence_threshold <= 1.0 AND
  vad_energy_threshold >= 0.0 AND vad_energy_threshold <= 1.0
),
ADD CONSTRAINT `chk_role_vad_silence_ms` CHECK (vad_silence_ms >= 100 AND vad_silence_ms <= 10000);

-- 设备表检查约束
ALTER TABLE `sys_device` 
ADD CONSTRAINT `chk_device_ip_format` CHECK (
  ip_address IS NULL OR 
  ip_address REGEXP '^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$' OR
  ip_address REGEXP '^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$'
);

-- 消息表检查约束
ALTER TABLE `sys_message` 
ADD CONSTRAINT `chk_message_audio_duration` CHECK (audio_duration IS NULL OR audio_duration >= 0),
ADD CONSTRAINT `chk_message_tokens_used` CHECK (tokens_used IS NULL OR tokens_used >= 0),
ADD CONSTRAINT `chk_message_processing_time` CHECK (processing_time_ms IS NULL OR processing_time_ms >= 0);

-- 验证码表检查约束
ALTER TABLE `sys_verification_code` 
ADD CONSTRAINT `chk_verification_code_length` CHECK (CHAR_LENGTH(verification_code) >= 4 AND CHAR_LENGTH(verification_code) <= 10);

-- =====================================================
-- 触发器（用于自动更新统计信息）
-- =====================================================

-- 更新用户设备数量的触发器
DELIMITER $$

CREATE TRIGGER `tr_device_insert_update_user_stats` 
AFTER INSERT ON `sys_device`
FOR EACH ROW
BEGIN
  UPDATE `sys_user` 
  SET `total_device` = (
    SELECT COUNT(*) FROM `sys_device` WHERE `user_id` = NEW.user_id
  )
  WHERE `user_id` = NEW.user_id;
END$$

CREATE TRIGGER `tr_device_delete_update_user_stats` 
AFTER DELETE ON `sys_device`
FOR EACH ROW
BEGIN
  UPDATE `sys_user` 
  SET `total_device` = (
    SELECT COUNT(*) FROM `sys_device` WHERE `user_id` = OLD.user_id
  )
  WHERE `user_id` = OLD.user_id;
END$$

-- 更新角色使用设备数量的触发器
CREATE TRIGGER `tr_device_insert_update_role_stats` 
AFTER INSERT ON `sys_device`
FOR EACH ROW
BEGIN
  IF NEW.role_id IS NOT NULL THEN
    UPDATE `sys_role` 
    SET `total_device` = (
      SELECT COUNT(*) FROM `sys_device` WHERE `role_id` = NEW.role_id
    )
    WHERE `role_id` = NEW.role_id;
  END IF;
END$$

CREATE TRIGGER `tr_device_update_role_stats` 
AFTER UPDATE ON `sys_device`
FOR EACH ROW
BEGIN
  -- 更新旧角色统计
  IF OLD.role_id IS NOT NULL THEN
    UPDATE `sys_role` 
    SET `total_device` = (
      SELECT COUNT(*) FROM `sys_device` WHERE `role_id` = OLD.role_id
    )
    WHERE `role_id` = OLD.role_id;
  END IF;
  
  -- 更新新角色统计
  IF NEW.role_id IS NOT NULL THEN
    UPDATE `sys_role` 
    SET `total_device` = (
      SELECT COUNT(*) FROM `sys_device` WHERE `role_id` = NEW.role_id
    )
    WHERE `role_id` = NEW.role_id;
  END IF;
END$$

CREATE TRIGGER `tr_device_delete_update_role_stats` 
AFTER DELETE ON `sys_device`
FOR EACH ROW
BEGIN
  IF OLD.role_id IS NOT NULL THEN
    UPDATE `sys_role` 
    SET `total_device` = (
      SELECT COUNT(*) FROM `sys_device` WHERE `role_id` = OLD.role_id
    )
    WHERE `role_id` = OLD.role_id;
  END IF;
END$$

-- 更新模板使用次数的触发器
CREATE TRIGGER `tr_message_update_template_usage` 
AFTER INSERT ON `sys_message`
FOR EACH ROW
BEGIN
  -- 如果消息元数据中包含模板ID，则更新模板使用次数
  IF JSON_EXTRACT(NEW.metadata, '$.template_id') IS NOT NULL THEN
    UPDATE `sys_template` 
    SET `usage_count` = `usage_count` + 1
    WHERE `template_id` = JSON_EXTRACT(NEW.metadata, '$.template_id');
  END IF;
END$$

DELIMITER ;

-- =====================================================
-- 性能优化建议注释
-- =====================================================

/*
性能优化建议：

1. 定期分析表统计信息：
   ANALYZE TABLE sys_user, sys_role, sys_device, sys_message, sys_template, sys_verification_code, sys_config;

2. 定期优化表：
   OPTIMIZE TABLE sys_message; -- 消息表数据量大，定期优化

3. 监控慢查询：
   SET GLOBAL slow_query_log = 'ON';
   SET GLOBAL long_query_time = 2;

4. 分区建议（数据量大时）：
   - sys_message 表可按创建时间分区
   - sys_verification_code 表可按创建时间分区

5. 缓存策略：
   - 用户信息、角色配置等可使用 Redis 缓存
   - 系统配置可使用应用级缓存
*/

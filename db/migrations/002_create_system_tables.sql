-- =====================================================
-- 数据库迁移脚本 - 002 创建系统表
-- 文件名: 002_create_system_tables.sql
-- 创建时间: 2025-01-10
-- 描述: 创建验证码、模板、消息等系统表
-- =====================================================

USE `xiaozhi`;

-- =====================================================
-- 验证码表 (sys_verification_code)
-- =====================================================
DROP TABLE IF EXISTS `sys_verification_code`;
CREATE TABLE `sys_verification_code` (
  `code_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `verification_code` varchar(100) NOT NULL COMMENT '验证码',
  `code_type` varchar(50) DEFAULT NULL COMMENT '验证码类型',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备ID',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `audio_path` text COMMENT '语音文件路径',
  `is_used` tinyint(1) DEFAULT 0 COMMENT '是否已使用：0-未使用，1-已使用',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`code_id`),
  KEY `idx_verification_code` (`verification_code`),
  KEY `idx_email` (`email`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_is_used` (`is_used`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证码表';

-- =====================================================
-- 模板表 (sys_template)
-- =====================================================
DROP TABLE IF EXISTS `sys_template`;
CREATE TABLE `sys_template` (
  `template_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID，主键',
  `user_id` int unsigned NOT NULL COMMENT '用户ID，模板所属用户',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_desc` text DEFAULT NULL COMMENT '模板描述',
  `template_content` longtext NOT NULL COMMENT '模板内容',
  `template_type` varchar(50) DEFAULT 'PROMPT' COMMENT '模板类型：PROMPT-提示词模板，FUNCTION-函数模板',
  `category` varchar(50) DEFAULT NULL COMMENT '模板分类',
  `tags` varchar(255) DEFAULT NULL COMMENT '标签，逗号分隔',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开：0-私有，1-公开',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认模板',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `status` enum('ACTIVE','INACTIVE','DRAFT') DEFAULT 'ACTIVE' COMMENT '模板状态',
  `version` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`template_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_template_name` (`template_name`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_category` (`category`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_template_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板表';

-- =====================================================
-- 消息记录表 (sys_message)
-- =====================================================
DROP TABLE IF EXISTS `sys_message`;
CREATE TABLE `sys_message` (
  `message_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '消息ID，主键',
  `user_id` int unsigned NOT NULL COMMENT '用户ID',
  `device_id` varchar(255) NOT NULL COMMENT '设备ID',
  `role_id` int unsigned DEFAULT NULL COMMENT '角色ID',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `message_type` enum('USER','ASSISTANT','SYSTEM') NOT NULL COMMENT '消息类型：USER-用户消息，ASSISTANT-助手消息，SYSTEM-系统消息',
  `content_type` enum('TEXT','AUDIO','IMAGE','FILE') DEFAULT 'TEXT' COMMENT '内容类型',
  `message_content` longtext NOT NULL COMMENT '消息内容',
  `audio_path` varchar(500) DEFAULT NULL COMMENT '音频文件路径',
  `audio_duration` int DEFAULT NULL COMMENT '音频时长(秒)',
  `model_name` varchar(100) DEFAULT NULL COMMENT '使用的模型名称',
  `tokens_used` int DEFAULT NULL COMMENT '使用的token数量',
  `processing_time_ms` int DEFAULT NULL COMMENT '处理时间(毫秒)',
  `status` enum('PENDING','PROCESSING','COMPLETED','FAILED') DEFAULT 'COMPLETED' COMMENT '消息状态',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `metadata` json DEFAULT NULL COMMENT '元数据，JSON格式',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`message_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_message_type` (`message_type`),
  KEY `idx_content_type` (`content_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_message_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_message_device` FOREIGN KEY (`device_id`) REFERENCES `sys_device` (`device_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_message_role` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`role_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息记录表';

-- =====================================================
-- 配置表 (sys_config)
-- =====================================================
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config` (
  `config_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID，主键',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` longtext NOT NULL COMMENT '配置值',
  `config_type` varchar(50) DEFAULT 'STRING' COMMENT '配置类型：STRING,NUMBER,BOOLEAN,JSON',
  `config_group` varchar(50) DEFAULT 'SYSTEM' COMMENT '配置分组',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `is_encrypted` tinyint(1) DEFAULT 0 COMMENT '是否加密存储',
  `is_readonly` tinyint(1) DEFAULT 0 COMMENT '是否只读',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

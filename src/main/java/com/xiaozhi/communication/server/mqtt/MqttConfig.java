package com.xiaozhi.communication.server.mqtt;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MQTT 配置类
 * 用于管理 MQTT 连接配置和属性绑定
 */
@Data
@Component
@ConfigurationProperties(prefix = "mqtt")
public class MqttConfig {

    /**
     * MQTT Broker 配置
     */
    private Broker broker = new Broker();

    /**
     * MQTT 客户端配置
     */
    private Client client = new Client();

    /**
     * Topic 配置
     */
    private Topic topic = new Topic();

    /**
     * 会话管理配置
     */
    private Session session = new Session();

    /**
     * UDP 服务器配置
     */
    private Udp udp = new Udp();

    /**
     * 加密配置
     */
    private Encryption encryption = new Encryption();

    @Data
    public static class Broker {
        /**
         * MQTT Broker 主机地址
         */
        private String host = "localhost";

        /**
         * MQTT Broker 端口
         */
        private int port = 1883;

        /**
         * 是否使用 SSL/TLS
         */
        private boolean ssl = false;

        /**
         * 用户名
         */
        private String username = "admin";

        /**
         * 密码
         */
        private String password = "',.pyf123";

        /**
         * 连接超时时间（秒）
         */
        private int connectionTimeout = 30;

        /**
         * 保持连接间隔（秒）
         */
        private int keepAliveInterval = 60;

        /**
         * 是否自动重连
         */
        private boolean automaticReconnect = true;

        /**
         * 是否清理会话
         */
        private boolean cleanSession = true;

        /**
         * 获取完整的 Broker URL（向后兼容）
         * @return 完整的 MQTT Broker URL
         */
        public String getUrl() {
            String protocol = ssl ? "ssl" : "tcp";
            return String.format("%s://%s:%d", protocol, host, port);
        }

        /**
         * 设置 URL（向后兼容，会自动解析为 host、port 和 ssl）
         * @param url 完整的 MQTT Broker URL
         */
        public void setUrl(String url) {
            if (url == null || url.trim().isEmpty()) {
                return;
            }

            try {
                // 解析协议
                if (url.startsWith("ssl://")) {
                    this.ssl = true;
                    url = url.substring(6);
                } else if (url.startsWith("tcp://")) {
                    this.ssl = false;
                    url = url.substring(6);
                } else {
                    // 默认为 tcp
                    this.ssl = false;
                }

                // 解析主机和端口
                int colonIndex = url.indexOf(':');
                if (colonIndex > 0) {
                    this.host = url.substring(0, colonIndex);
                    try {
                        this.port = Integer.parseInt(url.substring(colonIndex + 1));
                    } catch (NumberFormatException e) {
                        // 如果端口解析失败，使用默认端口
                        this.port = ssl ? 8883 : 1883;
                    }
                } else {
                    this.host = url;
                    this.port = ssl ? 8883 : 1883;
                }
            } catch (Exception e) {
                // 解析失败时使用默认值
                this.host = "localhost";
                this.port = 1883;
                this.ssl = false;
            }
        }
    }

    @Data
    public static class Client {
        /**
         * 客户端 ID
         */
        private String id = "xiaozhi-server";

        /**
         * 是否在客户端 ID 后添加随机后缀
         */
        private boolean randomSuffix = false;
    }

    @Data
    public static class Topic {
        /**
         * Topic 前缀
         */
        private String prefix = "xiaozhi";

        /**
         * QoS 等级
         */
        private int qos = 0;
    }

    @Data
    public static class Session {
        /**
         * 会话超时时间（秒）
         */
        private int timeout = 300;

        /**
         * 心跳间隔（秒）
         */
        private int heartbeatInterval = 60;

        /**
         * 会话清理检查间隔（秒）
         */
        private int cleanupInterval = 30;
    }

    @Data
    public static class Udp {
        /**
         * UDP 服务器端口
         */
        private int port = 8884;

        /**
         * UDP 服务器主机
         */
        private String host = "0.0.0.0";

        /**
         * 接收缓冲区大小
         */
        private int receiveBufferSize = 65536;

        /**
         * 发送缓冲区大小
         */
        private int sendBufferSize = 65536;

        /**
         * 工作线程数
         */
        private int workerThreads = 4;

        /**
         * 会话超时时间（毫秒）
         */
        private long sessionTimeoutMs = 300000;
    }

    @Data
    public static class Encryption {
        /**
         * 加密算法
         */
        private String algorithm = "AES-128-CTR";

        /**
         * 密钥长度
         */
        private int keyLength = 16;

        /**
         * Nonce 长度
         */
        private int nonceLength = 16;

        /**
         * 序列号窗口大小
         */
        private int sequenceWindowSize = 1000;
    }
}

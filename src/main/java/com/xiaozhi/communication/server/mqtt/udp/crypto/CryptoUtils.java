package com.xiaozhi.communication.server.mqtt.udp.crypto;

import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * 加密工具类
 * 实现 AES-128-CTR 加密解密功能
 */
@Slf4j
@Component
public class CryptoUtils {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CTR/NoPadding";
    private static final int KEY_LENGTH = 16; // 128 bits
    private static final int NONCE_LENGTH = 16; // 128 bits

    private static final SecureRandom secureRandom = new SecureRandom();

    /**
     * 生成随机加密密钥
     */
    public static byte[] generateKey(int length) {
        var key = new byte[length];
        secureRandom.nextBytes(key);
        return key;
    }


    /**
     * 加密数据
     *
     * @param data     要加密的数据
     * @param key      加密密钥 (16字节)
     * @param nonce    基础 nonce (16字节)
     * @return 加密后的数据
     */
    public static Try<byte[]> encrypt(byte[] data, byte[] key, byte[] nonce) {
        return Try.of(() -> {
            var cipher = Cipher.getInstance(TRANSFORMATION);
            var keySpec = new SecretKeySpec(key, ALGORITHM);
            var ivSpec = new IvParameterSpec(nonce);

            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            return cipher.doFinal(data);
        });
    }

    /**
     * 解密数据
     *
     * @param encryptedData 加密的数据
     * @param key           解密密钥 (16字节)
     * @param nonce         基础 nonce (16字节)
     * @param sequence      序列号，用于构建完整的 IV
     * @return 解密后的数据，失败返回 null
     */
    public static byte[] decrypt(byte[] encryptedData, byte[] key, byte[] nonce, int sequence) {
        try {
            if (key.length != KEY_LENGTH) {
                log.error("密钥长度错误，期望 {} 字节，实际 {} 字节", KEY_LENGTH, key.length);
                return null;
            }

            if (nonce.length != NONCE_LENGTH) {
                log.error("Nonce 长度错误，期望 {} 字节，实际 {} 字节", NONCE_LENGTH, nonce.length);
                return null;
            }

            // 构建 IV：前12字节使用 nonce，后4字节使用序列号
            byte[] iv = new byte[16];
            System.arraycopy(nonce, 0, iv, 0, 12);
            ByteBuffer.wrap(iv, 12, 4).putInt(sequence);

            // 创建解密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            SecretKeySpec keySpec = new SecretKeySpec(key, ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            return cipher.doFinal(encryptedData);

        } catch (Exception e) {
            log.error("解密数据时发生错误 - Sequence: {}", sequence, e);
            return null;
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 将十六进制字符串转换为字节数组
     */
    public static byte[] hexToBytes(String hex) {
        try {
            int len = hex.length();
            byte[] data = new byte[len / 2];
            for (int i = 0; i < len; i += 2) {
                data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                        + Character.digit(hex.charAt(i + 1), 16));
            }
            return data;
        } catch (Exception e) {
            log.error("十六进制字符串转换失败: {}", hex, e);
            return null;
        }
    }


    /**
     * 验证密钥格式
     */
    public boolean isValidKey(byte[] key) {
        return key != null && key.length == KEY_LENGTH;
    }

    /**
     * 验证 Nonce 格式
     */
    public boolean isValidNonce(byte[] nonce) {
        return nonce != null && nonce.length == NONCE_LENGTH;
    }

    /**
     * 密钥对
     */
    public record KeyPair(byte[] key, byte[] nonce) {
        public String getKeyHex() {
            return toHex(key);
        }

        public String getNonceHex() {
            return toHex(nonce);
        }

        private static String toHex(byte[] bytes) {
            StringBuilder result = new StringBuilder();
            for (byte b : bytes) {
                result.append(String.format("%02x", b));
            }
            return result.toString();
        }
    }
}

package com.xiaozhi.communication.server.mqtt.udp;

import lombok.Data;

import java.net.InetSocketAddress;
import java.util.HexFormat;

/**
 * UDP 信息
 * 存储每个 UDP 连接的会话状态和加密信息
 */
@Data
public class UdpInfo {
    
    private final String sessionId;
    private final byte[] key;
    private final byte[] nonce;
    private InetSocketAddress clientAddress;
    private int localSequence = 0;
    private int remoteSequence = 0;
    
    public UdpInfo(String sessionId, byte[] key, byte[] nonce) {
        this.sessionId = sessionId;
        this.key = key.clone();
        this.nonce = nonce.clone();
    }

    public void incrementLocalSequence() {
        this.localSequence += 1;
    }

    public String getKeyHex() {
        return HexFormat.of().formatHex(key);
    }

    public String getNonceHex() {
        return HexFormat.of().formatHex(nonce);
    }
}

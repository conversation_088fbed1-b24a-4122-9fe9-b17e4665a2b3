package com.xiaozhi.communication.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaozhi.utils.JsonUtil;
import lombok.Data;

/**
 * 基础消息类
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class BaseMessage {

    protected MessageType type;

    @JsonProperty("session_id")
    protected String sessionId;

    public BaseMessage(MessageType messageType) {
        this.type = messageType;
    }

    public BaseMessage(MessageType messageType, String sessionId) {
        this.type = messageType;
        this.sessionId = sessionId;
    }
}

package com.xiaozhi.communication.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * TTS消息
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TtsMessage extends BaseMessage {

    private String text;

    private String state;

    public TtsMessage() {
        super(MessageType.TTS);
    }

    public TtsMessage(String state) {
        super(MessageType.TTS);
        this.state = state;
    }

    public TtsMessage(String text, String state) {
        super(MessageType.TTS);
        this.text = text;
        this.state = state;
    }

}

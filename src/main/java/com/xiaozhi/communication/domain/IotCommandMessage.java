package com.xiaozhi.communication.domain;

import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * IoT命令消息
 */
@Getter
public class IotCommandMessage extends BaseMessage {

    private List<Map<String, Object>> commands;

    public IotCommandMessage() {
        super(MessageType.IOT);
    }

    public IotCommandMessage(String sessionId, List<Map<String, Object>> commands) {
        super(MessageType.IOT, sessionId);
        this.commands = commands;
    }

}

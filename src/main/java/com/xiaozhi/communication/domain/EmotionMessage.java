package com.xiaozhi.communication.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * 表情消息
 */
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmotionMessage extends BaseMessage {

    @JsonProperty("emotion")
    private String emotion;

    @JsonProperty("text")
    private String text;

    public EmotionMessage() {
        super(MessageType.LLM);
    }

    public EmotionMessage(String sessionId, String emotion) {
        super(MessageType.LLM, sessionId);
        this.emotion = emotion;
        this.text = emotion; // 表情消息中text和emotion相同
    }
}

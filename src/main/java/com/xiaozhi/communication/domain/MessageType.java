package com.xiaozhi.communication.domain;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 消息类型枚举
 */
@Getter
public enum MessageType {
    /**
     * 文本转语音消息
     */
    TTS("tts"),
    
    /**
     * 语音转文本消息
     */
    STT("stt"),
    
    /**
     * IoT设备命令消息
     */
    IOT("iot"),
    
    /**
     * 大语言模型消息（包含表情）
     */
    LLM("llm");

    @JsonValue
    private final String value;
    
    MessageType(String value) {
        this.value = value;
    }

}

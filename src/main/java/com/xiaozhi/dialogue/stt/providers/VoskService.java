package com.xiaozhi.dialogue.stt.providers;

import java.io.ByteArrayInputStream;

import com.xiaozhi.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.vosk.LibVosk;
import org.vosk.LogLevel;
import org.vosk.Model;
import org.vosk.Recognizer;

import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.utils.AudioUtils;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

@Slf4j
public class VoskService implements SttService {
    private static final String PROVIDER_NAME = "vosk";

    private Model model;
    private boolean modelLoaded = false;

    // @PostConstruct
    public void initialize() throws Exception {
        try {
            // 检查是否是 macOS 操作系统
            var osName = System.getProperty("os.name").toLowerCase();
            // 检查是否是 ARM 架构（用于 M 系列芯片）
            var osArch = System.getProperty("os.arch").toLowerCase();

            // 如果是 macOS 并且是 ARM 架构（M 系列芯片）
            if (osName.contains("mac") && osArch.contains("aarch64")) {
                System.load(STR."\{System.getProperty("user.dir")}/lib/libvosk.dylib");
                log.info("Vosk library loaded for macOS M-series chip.");
            }
            // 禁用Vosk日志输出
            LibVosk.setLogLevel(LogLevel.WARNINGS);

            // 加载模型，路径为配置的模型目录
            var voskModelPath = STR."\{System.getProperty("user.dir")}/models/vosk-model";
            model = new Model(voskModelPath);
            modelLoaded = true;
            log.info("Vosk 模型加载成功！路径: {}", voskModelPath);
        } catch (Exception e) {
            log.warn("Vosk 模型加载失败！将使用其他STT服务: {}", e.getMessage());
        }
    }

    /**
     * 检查模型是否成功加载
     *
     * @return 如果模型加载成功返回true，否则返回false
     */
    public boolean isModelLoaded() {
        return modelLoaded && model != null;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String recognition(byte[] audioData) {
        if (!isModelLoaded()) {
            log.error("Vosk模型未加载，无法进行识别！");
            return null;
        }

        if (audioData == null || audioData.length == 0) {
            log.warn("音频数据为空！");
            return null;
        }

        try (Recognizer recognizer = new Recognizer(model, AudioUtils.SAMPLE_RATE)) {
            ByteArrayInputStream audioStream = new ByteArrayInputStream(audioData);

            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = audioStream.read(buffer)) != -1) {
                if (recognizer.acceptWaveForm(buffer, bytesRead)) {
                    // 如果识别到完整的结果
                    return JsonUtil.parse(recognizer.getResult(), RecognitionResult.class)
                            .map(it -> it.text.replaceAll("\\s+", ""))
                            .getOrElse("");
                }
            }

            // 返回最终的识别结果
            return JsonUtil.parse(recognizer.getFinalResult(), RecognitionResult.class)
                    .map(it -> it.text.replaceAll("\\s+", ""))
                    .getOrElse("");
        } catch (Exception e) {
            log.error("处理音频时发生错误！", e);
            return null;
        }
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        if (!isModelLoaded()) {
            log.error("Vosk模型未加载，无法进行流式识别！");
            return null;
        }

        try (var recognizer = new Recognizer(model, AudioUtils.SAMPLE_RATE)) {
            return Flux.create(sink -> audioSink.asFlux()
                            .subscribe(
                                    chunk -> recognizer.acceptWaveForm(chunk, chunk.length),
                                    sink::error,
                                    () -> {
                                        var text = JsonUtil.parse(recognizer.getFinalResult(), RecognitionResult.class)
                                                .map(it -> it.text.replaceAll("\\s+", ""))
                                                .getOrElse("");

                                        log.debug("Vosk识别最终结果: {}", text);

                                        sink.next(text);
                                        sink.complete();
                                    }
                            )).reduce(new StringBuilder(), StringBuilder::append)
                    .blockOptional()
                    .map(StringBuilder::toString)
                    .orElse("");
        } catch (Exception e) {
            log.error("Vosk 语音识别失败 {}", e.getMessage());
        }

        return "";
    }

    record RecognitionResult(String text) {
    }
}
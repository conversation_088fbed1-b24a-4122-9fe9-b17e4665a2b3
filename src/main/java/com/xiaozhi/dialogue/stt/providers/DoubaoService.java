package com.xiaozhi.dialogue.stt.providers;

import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.JsonUtil;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import org.jetbrains.annotations.NotNull;
import reactor.core.publisher.Sinks;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * 豆包(DouBao) ASR 语音识别服务实现
 * 基于 WebSocket 协议的流式语音识别服务
 */
@Slf4j
public class DoubaoService implements SttService {

    // 协议常量
    private static final byte PROTOCOL_VERSION = 0b0001;
    private static final byte DEFAULT_HEADER_SIZE = 0b0001;
    private static final byte FULL_CLIENT_REQUEST = 0b0001;
    private static final byte AUDIO_ONLY_REQUEST = 0b0010;
    private static final byte FULL_SERVER_RESPONSE = 0b1001;
    private static final byte SERVER_ACK = 0b1011;
    private static final byte SERVER_ERROR_RESPONSE = 0b1111;
    private static final byte POS_SEQUENCE = 0b0001;
    private static final byte NEG_WITH_SEQUENCE = 0b0011;
    private static final byte JSON = 0b0001;
    private static final byte GZIP = 0b0001;

    private static final String PROVIDER_NAME = "doubao";
    private static final int QUEUE_TIMEOUT_MS = 100;
    private static final long RECOGNITION_TIMEOUT_MS = 30000; // 30秒超时

    private final String apiUrl;
    private final String appId;
    private final String apiKey;

    public DoubaoService(SysConfig config) {
        this.apiUrl = config.getApiUrl();
        this.appId = config.getAppId();
        this.apiKey = config.getApiKey();
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String recognition(byte[] audioData) {
        log.warn("豆包ASR不支持单次识别，请使用流式识别");
        return "";
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        if (apiUrl == null || appId == null || apiKey == null) {
            log.error("豆包ASR配置未设置，无法进行识别");
            return "";
        }

        var responseQueue = new LinkedBlockingQueue<byte[]>();
        var isRunning = new AtomicBoolean(true);
        var finalResult = new AtomicReference<>("");
        var recognitionLatch = new CountDownLatch(1);
        var sequenceNumber = new AtomicInteger(1);

        // 创建WebSocket客户端
        OkHttpClient client = new OkHttpClient.Builder()
                .pingInterval(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .callTimeout(60, TimeUnit.SECONDS)
                .build();

        Request request = new Request.Builder()
                .url(apiUrl)
                .header("X-Api-App-Key", appId)
                .header("X-Api-Access-Key", apiKey)
                .header("X-Api-Resource-Id", "volc.bigasr.sauc.duration")
                .header("X-Api-Connect-Id", UUID.randomUUID().toString())
                .build();

        WebSocket webSocket = client.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
                log.debug("豆包ASR WebSocket连接已建立, X-Tt-Logid: {}", response.header("X-Tt-Logid"));

                try {
                    // 发送完整客户端请求
                    sendFullClientRequest(webSocket, sequenceNumber.get());

                    audioSink.asFlux()
                            .subscribe(
                                    audioChunk -> {
                                        if (isRunning.get() && audioChunk != null && audioChunk.length > 0) {
                                            int seq = sequenceNumber.incrementAndGet();
                                            sendAudioSegment(webSocket, audioChunk, audioChunk.length, false, seq);
                                        }
                                    },
                                    error -> {
                                        log.error("音频流处理错误", error);
                                        isRunning.set(false);
                                        // 发送结束标志
                                        int seq = sequenceNumber.incrementAndGet();
                                        sendAudioSegment(webSocket, new byte[0], 0, true, -seq);
                                    },
                                    () -> {
                                        log.debug("音频流结束，发送最后一个分段");
                                        // 发送结束标志
                                        int seq = sequenceNumber.incrementAndGet();
                                        sendAudioSegment(webSocket, new byte[0], 0, true, -seq);
                                    });

                    // 启动响应处理线程
                    Thread.startVirtualThread(() -> {
                        try {
                            processResponses(responseQueue, isRunning, finalResult, recognitionLatch);
                        } catch (Exception e) {
                            log.error("处理响应时发生错误", e);
                            isRunning.set(false);
                        }
                    });

                } catch (Exception e) {
                    log.error("初始化WebSocket处理失败", e);
                    isRunning.set(false);
                    recognitionLatch.countDown();
                }
            }

            @Override
            public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
                log.debug("收到文本消息: {}", text);
            }

            @Override
            public void onMessage(@NotNull WebSocket webSocket, @NotNull ByteString bytes) {
                responseQueue.offer(bytes.toByteArray());
            }

            @Override
            public void onClosing(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
                log.debug("WebSocket连接正在关闭: code={}, reason={}", code, reason);
                isRunning.set(false);
            }

            @Override
            public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, Response response) {
                log.error("WebSocket连接失败", t);
                isRunning.set(false);
                recognitionLatch.countDown();
            }
        });

        try {
            // 等待识别完成或超时
            boolean recognized = recognitionLatch.await(RECOGNITION_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            if (!recognized) {
                log.warn("豆包ASR识别超时");
            }
        } catch (InterruptedException e) {
            log.error("识别过程被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 清理资源
            cleanup(webSocket, client);
        }

        return finalResult.get();
    }

    /**
     * 发送完整客户端请求
     */
    private void sendFullClientRequest(WebSocket webSocket, int seq) throws IOException {
        var payload = Map.of(
                "user", Map.of(
                        "uid", "xiaozhi"),
                "audio", Map.of(
                        "format", "pcm",
                        "sample_rate", 16000,
                        "bits", 16,
                        "channel", 1,
                        "codec", "raw"),
                "request", Map.of(
                        "model_name", "bigmodel",
                        "enable_punc", true));

        var payloadStr = JsonUtil.toJson(payload);
        log.debug("发送完整客户端请求: {}", payloadStr);

        var payloadBytes = gzipCompress(payloadStr.getBytes());
        var header = getHeader(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON, GZIP, (byte) 0);
        var payloadSize = intToBytes(payloadBytes.length);
        var seqBytes = intToBytes(seq);

        var fullClientRequest = new byte[header.length + seqBytes.length + payloadSize.length + payloadBytes.length];
        System.arraycopy(header, 0, fullClientRequest, 0, header.length);
        System.arraycopy(seqBytes, 0, fullClientRequest, header.length, seqBytes.length);
        System.arraycopy(payloadSize, 0, fullClientRequest, header.length + seqBytes.length, payloadSize.length);
        System.arraycopy(payloadBytes, 0, fullClientRequest, header.length + seqBytes.length + payloadSize.length,
                payloadBytes.length);

        webSocket.send(ByteString.of(fullClientRequest));
    }

    /**
     * 发送音频分段
     */
    private void sendAudioSegment(WebSocket webSocket, byte[] buffer, int len, boolean isLast, int seq) {
        try {
            var messageTypeSpecificFlags = isLast ? NEG_WITH_SEQUENCE : POS_SEQUENCE;
            var header = getHeader(AUDIO_ONLY_REQUEST, messageTypeSpecificFlags, JSON, GZIP, (byte) 0);
            var sequenceBytes = intToBytes(seq);
            var payloadBytes = gzipCompress(buffer, len);
            var payloadSize = intToBytes(payloadBytes.length);

            log.debug("发送音频分段 - 序号: {}, 原始长度: {}, 压缩后长度: {}, 是否最后: {}",
                    seq, len, payloadBytes.length, isLast);

            var audioRequest = new byte[header.length + sequenceBytes.length + payloadSize.length + payloadBytes.length];
            System.arraycopy(header, 0, audioRequest, 0, header.length);
            System.arraycopy(sequenceBytes, 0, audioRequest, header.length, sequenceBytes.length);
            System.arraycopy(payloadSize, 0, audioRequest, header.length + sequenceBytes.length, payloadSize.length);
            System.arraycopy(payloadBytes, 0, audioRequest, header.length + sequenceBytes.length + payloadSize.length,
                    payloadBytes.length);

            webSocket.send(ByteString.of(audioRequest));
        } catch (Exception e) {
            log.error("发送音频分段失败 - 序号: {}, 长度: {}, 是否最后: {}", seq, len, isLast, e);
            throw e;
        }
    }

    /**
     * 处理响应消息
     */
    private void processResponses(BlockingQueue<byte[]> responseQueue, AtomicBoolean isRunning,
                                  AtomicReference<String> finalResult, CountDownLatch recognitionLatch) {
        while (isRunning.get() || !responseQueue.isEmpty()) {
            try {
                byte[] response = responseQueue.poll(QUEUE_TIMEOUT_MS, TimeUnit.MILLISECONDS);
                if (response != null) {
                    parseResponse(response)
                            .forEach(res -> {
                                for (Utterance utterance : res.result.utterances) {
                                    if (utterance.definite) {
                                        finalResult.set(utterance.text.trim());
                                        break;
                                    }
                                }
                            });
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        recognitionLatch.countDown();
    }

    /**
     * 解析响应消息
     */
    private Try<RecognitionResult> parseResponse(byte[] res) {
        return Option.when(res.length >= 4, res)
                .toTry()
                .flatMap(bytes -> {
                    // 解析 header (4 bytes)
                    final var num = 0b00001111;
                    var message_type = (bytes[1] >> 4) & num;

                    // 跳过头部信息（12 bytes)
                    var payload = new byte[bytes.length - 12];
                    System.arraycopy(bytes, 12, payload, 0, payload.length);
                    var payloadStr = new String(payload);

                    // 服务端错误
                    if (message_type == SERVER_ERROR_RESPONSE) {
                        return Try.failure(new Throwable(payloadStr));
                    }

                    // 解析识别结果
                    return JsonUtil.parse(payloadStr, RecognitionResult.class)
                            .onFailure(e -> log.warn("解析识别结果JSON失败: {}", e.getMessage()));
                });
    }

    /**
     * 检查是否为最后一个包
     */
    private boolean isLastPacket(byte[] res) {
        if (res == null || res.length < 8) {
            return false;
        }

        try {
            // 解析序列号
            byte[] temp = new byte[4];
            System.arraycopy(res, 4, temp, 0, temp.length);
            int sequence = bytesToInt(temp);
            return sequence < 0; // 负序列号表示最后一个包
        } catch (Exception e) {
            log.error("检查最后包失败", e);
            return false;
        }
    }

    /**
     * 清理资源
     */
    private void cleanup(WebSocket webSocket, OkHttpClient client) {
        try {
            // 关闭WebSocket
            if (webSocket != null) {
                webSocket.close(1000, "正常关闭");
            }

            // 关闭OkHttpClient线程池
            client.dispatcher().executorService().shutdown();
            if (!client.dispatcher().executorService().awaitTermination(5, TimeUnit.SECONDS)) {
                client.dispatcher().executorService().shutdownNow();
            }
        } catch (Exception e) {
            log.error("清理资源时发生错误", e);
        }
    }

    // ==================== 协议工具方法 ====================

    /**
     * 构建协议头部
     */
    private byte[] getHeader(byte messageType, byte messageTypeSpecificFlags,
                             byte serialMethod, byte compressionType, byte reservedData) {
        final byte[] header = new byte[4];
        header[0] = (byte) ((PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE);
        header[1] = (byte) ((messageType << 4) | messageTypeSpecificFlags);
        header[2] = (byte) ((serialMethod << 4) | compressionType);
        header[3] = reservedData;
        return header;
    }

    /**
     * 整数转字节数组
     */
    private byte[] intToBytes(int a) {
        return new byte[]{
                (byte) ((a >> 24) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) (a & 0xFF)
        };
    }

    /**
     * 字节数组转整数
     */
    private int bytesToInt(byte[] src) {
        if (src == null || (src.length != 4)) {
            throw new IllegalArgumentException("Invalid byte array for int conversion");
        }
        return ((src[0] & 0xFF) << 24)
                | ((src[1] & 0xff) << 16)
                | ((src[2] & 0xff) << 8)
                | ((src[3] & 0xff));
    }

    /**
     * GZIP压缩
     */
    private byte[] gzipCompress(byte[] src) {
        return gzipCompress(src, src.length);
    }

    /**
     * GZIP压缩（指定长度）
     */
    private byte[] gzipCompress(byte[] src, int len) {
        if (src == null || len == 0) {
            // 对于空数据，返回一个有效的空GZIP数据
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
                // 写入空数据但确保GZIP流完整
                gzip.finish();
            } catch (IOException e) {
                log.error("GZIP压缩空数据失败", e);
                return new byte[0];
            }
            return out.toByteArray();
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
            gzip.write(src, 0, len);
            gzip.finish(); // 确保所有数据被写入
        } catch (IOException e) {
            log.error("GZIP压缩失败", e);
            return new byte[0];
        }
        return out.toByteArray();
    }

    /**
     * GZIP解压缩
     */
    private byte[] gzipDecompress(byte[] src) {
        if (src == null || src.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream ins = new ByteArrayInputStream(src);
        try (GZIPInputStream gzip = new GZIPInputStream(ins)) {
            byte[] buffer = new byte[256];
            int len;
            while ((len = gzip.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            log.error("GZIP解压缩失败", e);
            return null;
        }
        return out.toByteArray();
    }

    /**
     * 流式识别成功响应
     * @param result
     */
    record RecognitionResult(Result result) {}

    record Result(String text, List<Utterance> utterances) {
        public Result {
            if (utterances == null) {
                utterances = List.of();
            }
        }
    }

    record Utterance(boolean definite, String text) {}
}

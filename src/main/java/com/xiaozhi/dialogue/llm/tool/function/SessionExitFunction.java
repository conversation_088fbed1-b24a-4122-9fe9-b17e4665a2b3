package com.xiaozhi.dialogue.llm.tool.function;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import com.xiaozhi.dialogue.llm.tool.ToolCallStringResultConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class SessionExitFunction implements ToolsGlobalRegistry.GlobalFunction {

    ToolCallback toolCallback = FunctionToolCallback
            .builder("func_stop_chat", (Map<String, String> params, ToolContext toolContext) -> {
                var chatSession = (ChatSession) toolContext.getContext().get(ChatService.TOOL_CONTEXT_SESSION_KEY);
                log.info("Call stop_chat function");
                chatSession.setCloseAfterChat(true);
                var farewell = params.get("farewell");
                return farewell == null ? "拜拜哟！" : farewell;
            })
            .toolMetadata(ToolMetadata.builder().returnDirect(true).build())
            .description("用于结束聊天")
            .inputSchema("""
                        {
                            "type": "object",
                            "properties": {
                                "farewell": {
                                    "type": "string",
                                    "description": "与用户友好结束对话的告别语"
                                }
                            },
                            "required": ["farewell"]
                        }
                    """)
            .inputType(Map.class)
            .toolCallResultConverter(ToolCallStringResultConverter.INSTANCE)
            .build();

    @Override
    public ToolCallback getFunctionCallTool(ChatSession chatSession) {
        return toolCallback;
    }
}

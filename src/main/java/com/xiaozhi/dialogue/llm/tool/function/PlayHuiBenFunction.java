package com.xiaozhi.dialogue.llm.tool.function;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.llm.tool.ToolCallStringResultConverter;
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import com.xiaozhi.dialogue.service.HuiBenService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class PlayHuiBenFunction implements ToolsGlobalRegistry.GlobalFunction {

    @Resource
    private HuiBenService huiBenService;

    ToolCallback toolCallback = FunctionToolCallback
            .builder("func_play_picture_book", (Map<String, String> params, ToolContext toolContext) -> {
                ChatSession chatSession = (ChatSession) toolContext.getContext().get(ChatService.TOOL_CONTEXT_SESSION_KEY);
                Integer num = MapUtil.getInt(params, "num");
                try {
                    if (num == null || num < 5 || num > 1100) {
                        num = RandomUtil.randomInt(5, 1100);
                    }
                    huiBenService.playHuiBen(chatSession, num);
                    return STR."尝试播放绘本《\{num}》";

                } catch (Exception e) {
                    log.error("播放绘本异常，绘本编号: {}", num, e);
                    return "绘本播放失败";
                }
            })
            .toolMetadata(ToolMetadata.builder().returnDirect(true).build())
            .description("Provide picture book playback function, need the book id")
            .inputSchema("""
                        {
                            "type": "object",
                            "properties": {
                                "book_id": {
                                    "type": "integer",
                                    "description": "the picture book id"
                                }
                            },
                            "required": ["book_id"]
                        }
                    """)
            .inputType(Map.class)
            .toolCallResultConverter(ToolCallStringResultConverter.INSTANCE)
            .build();

    @Override
    public ToolCallback getFunctionCallTool(ChatSession chatSession) {
        return toolCallback;
    }
}
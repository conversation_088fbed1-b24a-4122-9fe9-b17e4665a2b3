package com.xiaozhi.dialogue.intent;

import com.xiaozhi.communication.common.ChatSession;

/**
 * 意图识别服务接口
 */
public interface IntentService {

    /**
     * 识别用户输入的意图
     * @param text 用户输入文本
     * @return JSON格式的意图识别结果
     */
    String recognize(String text);

    /**
     * 识别用户输入的意图（带会话上下文）
     * @param text 用户输入文本
     * @param session 聊天会话，包含注册的工具信息
     * @return JSON格式的意图识别结果
     */
    String recognize(ChatSession session, String text);

}

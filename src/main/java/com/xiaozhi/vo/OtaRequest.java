package com.xiaozhi.vo;

import lombok.Data;

import java.util.List;

/**
 * OTA 接口请求参数
 */
@Data
public class OtaRequest {
    private int version;
    private String language;
    private int flashSize;
    private int minimumFreeHeapSize;
    private String macAddress;
    private String chipModelName;
    private String uuid;
    private Application application;
    private List<PartitionTable> partitionTable;
    private Board board;


    /**
     * 设备当前固件版本信息
     */
    @Data
    public static class Application {
        private String name;
        // 当前固件版本号
        private String version;
        private String compileTime;
        private String idfVersion;
        // 用于校验固件文件完整性Hash
        private String elfSha256;
    }

    /**
     * 设备分区表，用于检查是否有足够的空间，用于下载固件
     */
    @Data
    public static class PartitionTable {
        private String label;
        private int type;
        private int subtype;
        private int address;
        private int size;
    }

    /**
     * 开发板类型与版本，以及所运行的环境
     */
    @Data
    public static class Board {
        // 开发板类型
        private String type;
        // 开发板SKU
        private String name;
        // 设备接入的 Wi-Fi 名字
        private String ssid;
        // 设备接入的 Wi-Fi 信号强度
        private int rssi;
        private int channel;
        private String ip;
        private String mac;
    }
}
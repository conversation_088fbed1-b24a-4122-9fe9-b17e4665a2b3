package com.xiaozhi.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * BaseQueryParams
 */
@Data
@Schema(name = "分页参数")
public class BaseQueryParams {
    /**
     * 页数 起始 0
     */
    @Schema(title = "页数, 起始 1")
    private Integer start = 1;

    /**
     * 每页条数
     */
    @Schema(title = "每页条数, 默认 10")
    private Integer limit = 10;

    public <T> Page<T> toPage() {
        return new Page<>((start / limit) + 1, limit);
    }

}

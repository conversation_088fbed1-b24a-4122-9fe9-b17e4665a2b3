package com.xiaozhi.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = true)
public class TemplateQueryParams extends BaseQueryParams {
    private Optional<String> templateName = Optional.empty();
    private Optional<String> category = Optional.empty();
    private Optional<String> state = Optional.empty();
    private Optional<Integer> userId = Optional.empty();
}

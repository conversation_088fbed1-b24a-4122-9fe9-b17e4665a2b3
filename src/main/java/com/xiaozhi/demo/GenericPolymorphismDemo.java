package com.xiaozhi.demo;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.ErrorInfo;
import io.vavr.control.Either;

/**
 * 演示泛型多态问题
 */
public class GenericPolymorphismDemo {

    public static void main(String[] args) {
        demonstratePolymorphismWorks();
        demonstrateGenericPolymorphismProblem();
        demonstrateWildcardSolution();
    }

    /**
     * 演示普通多态是工作的
     */
    private static void demonstratePolymorphismWorks() {
        System.out.println("=== 普通多态工作正常 ===");
        
        // 这是可以的 - 普通的多态
        BizError bizError = BizError.UserNotExists;
        ErrorInfo errorInfo = bizError;  // ✅ 多态工作正常
        
        System.out.println("BizError 作为 ErrorInfo: " + errorInfo.getMsg());
        
        // 方法参数的多态也工作正常
        processError(bizError);  // ✅ 可以传入 BizError
    }

    /**
     * 演示泛型多态的问题
     */
    private static void demonstrateGenericPolymorphismProblem() {
        System.out.println("\n=== 泛型多态问题 ===");
        
        // 创建一个 Either<BizError, String>
        Either<BizError, String> bizErrorEither = Either.left(BizError.UserNotExists);
        
        // ❌ 这是不允许的 - 泛型不支持协变
        // Either<ErrorInfo, String> errorInfoEither = bizErrorEither;  // 编译错误！
        
        System.out.println("不能直接将 Either<BizError, String> 赋值给 Either<ErrorInfo, String>");
        
        // 需要显式转换
        Either<ErrorInfo, String> errorInfoEither = bizErrorEither.mapLeft(error -> (ErrorInfo) error);
        System.out.println("需要显式转换: " + errorInfoEither);
    }

    /**
     * 演示通配符解决方案
     */
    private static void demonstrateWildcardSolution() {
        System.out.println("\n=== 通配符解决方案 ===");
        
        Either<BizError, String> bizErrorEither = Either.left(BizError.UserNotExists);
        
        // 使用有界通配符
        Either<? extends ErrorInfo, String> wildcardEither = bizErrorEither;  // ✅ 这是可以的
        
        System.out.println("使用通配符: " + wildcardEither);
        
        // 但是通配符有使用限制
        processEitherWithWildcard(wildcardEither);
    }

    // 接受 ErrorInfo 的方法
    private static void processError(ErrorInfo error) {
        System.out.println("处理错误: " + error.getMsg());
    }

    // 接受通配符的方法
    private static void processEitherWithWildcard(Either<? extends ErrorInfo, String> either) {
        either.fold(
            error -> {
                System.out.println("错误: " + error.getMsg());
                return null;
            },
            success -> {
                System.out.println("成功: " + success);
                return null;
            }
        );
    }

    /**
     * 演示为什么方法返回类型不能使用通配符
     */
    // ❌ 这样的方法签名是有问题的
    // public Either<? extends ErrorInfo, String> problematicMethod() {
    //     return Either.left(BizError.UserNotExists);  // 调用者不知道具体的错误类型
    // }

    /**
     * 正确的方法签名
     */
    public Either<BizError, String> correctMethod() {
        return Either.left(BizError.UserNotExists);  // ✅ 明确的类型
    }

    /**
     * 如果真的需要返回 ErrorInfo，可以这样做
     */
    public Either<ErrorInfo, String> errorInfoMethod() {
        Either<BizError, String> result = Either.left(BizError.UserNotExists);
        return result.mapLeft(error -> (ErrorInfo) error);  // 显式转换
    }
}

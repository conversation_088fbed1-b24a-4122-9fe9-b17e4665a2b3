package com.xiaozhi.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 智能体实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysAgent extends BaseEntity {

    /** 智能体名称 */
    private String name;

    /** 平台智能体ID */
    private String botId;

    /** 智能体描述 */
    private String intro;

    /** 图标URL */
    private String iconUrl;

    /** 发布时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    // ========== 从 SysConfig 继承的字段（智能体配置信息） ==========

    /**
     * 配置ID
     */
    private Integer configId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 角色ID
     */
    private Integer roleId;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置描述
     */
    private String configDesc;

    /**
     * 配置类型（llm\stt\tts）
     */
    private String configType;

    /**
     * 模型类型（chat\vision\intent\embedding）
     */
    private String modelType;

    /**
     * 服务提供商 (openai\quen\vosk\aliyun\tencent等)
     */
    private String provider;

    /**
     * APP ID
     */
    private String appId;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * API密钥
     */
    private String apiSecret;

    /**
     * AK
     */
    private String ak;

    /**
     * SK
     */
    private String sk;

    /**
     * API URL
     */
    private String apiUrl;

    /**
     * 状态
     */
    private String state;

    /**
     * 是否默认
     */
    private Boolean isDefault;


}
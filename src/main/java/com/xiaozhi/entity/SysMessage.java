package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xiaozhi.utils.AudioUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.nio.file.Paths;
import java.text.SimpleDateFormat;

/**
 * 聊天记录表
 *
 * <AUTHOR>
 *
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysMessage extends BaseEntity {
    /**
     * 消息类型 - 普通消息
     */
    public static final String MESSAGE_TYPE_NORMAL = "NORMAL";
    /**
     * 消息类型 - 函数调用消息
     */
    public static final String MESSAGE_TYPE_FUNCTION_CALL = "FUNCTION_CALL";
    /**
     * 消息类型 - MCP消息
     */
    public static final String MESSAGE_TYPE_MCP = "MCP";

    private String deviceId;

    private String sender;

    private String content;

    private String audioPath;

    private String type = "NORMAL";

    private String sessionId;

    private Integer roleId;

    //辅助字段，不对应数据库表
    @TableField(exist = false)
    private String roleName;
    @TableField(exist = false)
    private String deviceName;

    public String getAudioPath() {
        if (this.createdAt == null) {
            return audioPath; // 分页会先进行一次处理，但是获取的为count(0)，没有实际字段会报错，这里直接返回
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HHmmss");
        String formattedTime = sdf.format(createdAt);

        String fileName = STR."\{formattedTime}-\{sender}.wav";

        return Paths.get(
                AudioUtils.AUDIO_PATH,
                deviceId.replace(":", "-"),
                String.valueOf(roleId),
                fileName
        ).toString();
    }

}
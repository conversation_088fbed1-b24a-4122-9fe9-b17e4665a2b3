package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * LLM\STT\TTS配置
 *
 * <AUTHOR>
 *
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysConfig extends BaseEntity {

    private Integer userId;

    /**
     * 配置名称
     */
    private String name;

    /**
     * 配置描述
     */
    private String intro;

    /**
     * 配置类型（llm\stt\tts）
     */
    private String type;

    /**
     * 模型类型（chat\vision\intent\embedding）
     */
    private String modelType;

    /**
     * 服务提供商 (openai\quen\vosk\aliyun\tencent等)
     */
    private String provider;

    private String appId;

    private String apiKey;

    private String apiSecret;

    private String ak;

    private String sk;

    private String apiUrl;

    private Boolean isEnabled;

    private Boolean isDefault;

    @TableField(exist = false)
    private double speed = 1;

}

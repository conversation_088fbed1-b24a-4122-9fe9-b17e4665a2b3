package com.xiaozhi.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 提示词模板实体类
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysTemplate extends BaseEntity {
    private String name;    // 模板名称
    private String intro;    // 模板描述
    private String content; // 模板内容
    private String category;        // 模板分类
    private Boolean isDefault;      // 是否默认模板(1是 0否)
    private Boolean isEnabled;          // 状态(1启用 0禁用)
    private Integer userId;
}
package com.xiaozhi.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 角色配置
 *
 * <AUTHOR>
 *
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysRole extends BaseEntity {

    private Integer userId;

    private String avatar;

    private String name;

    private String intro;

    private String voice;

    private Integer ttsId;

    private Integer modelId;

    private Integer sttId;

    private Boolean isDefault;

    private Boolean isEnabled;

}

package com.xiaozhi.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 设备表
 *
 * <AUTHOR>
 *
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysDevice extends BaseEntity {

    private String deviceId;

    /**
     * 用户ID - 设备所属用户
     */
    private Integer userId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备状态
     */
    private Boolean isOnline;


    /**
     * WiFi名称
     */
    private String wifiName;

    /**
     * IP
     */
    private String loginIp;

    /**
     * 芯片型号
     */
    private String chipModelName;

    /**
     * 芯片类型
     */
    private String type;

    /**
     * 固件版本
     */
    private String version;

    /**
     * 可用全局function的名称列表(逗号分割)，为空则使用所有全局function
     */
    private String functionNames;

    /**
     * 角色ID - 设备关联的角色
     */
    private Integer roleId;

    /**
     * 最后在线时间
     */
    private Date lastLoginTime;
}
package com.xiaozhi.entity;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.xiaozhi.communication.domain.*;
import lombok.Data;

@Data
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "type",
        visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = HelloMessage.class, name = "hello"),
})
public class ApiKey {
    protected String type;

    public ApiKey() {
        this.type = "general";
    }

    public ApiKey(String type) {
        this.type = type;
    }
}

package com.xiaozhi.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.vavr.control.Try;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class JsonUtil {
    private static final Logger logger = LoggerFactory.getLogger(JsonUtil.class);

    @Resource
    private ObjectMapper springObjectMapper;

    private static ObjectMapper mapper;

    @PostConstruct
    private void init() {
        mapper = springObjectMapper;
    }

    public static <T> String toJson(T obj) {
        String json = null;

        try {
            json = mapper.writeValueAsString(obj);
        } catch (Exception e) {
            logger.error("JsonUtil.toJson error", e);
        }

        return json;
    }

    public static <T> T fromJson(String json, Class<T> type) {
        T pojo = null;

        try {
            pojo = mapper.readValue(json, type);
        } catch (Exception e) {
            logger.error("JsonUtil.toJson error", e);
        }
        return pojo;
    }

    public static <T> T fromJson(String json, TypeReference<T> valueTypeRef) {
        T pojo = null;

        try {
            pojo = mapper.readValue(json, valueTypeRef);
        } catch (Exception e) {
            logger.error("JsonUtil.toJson error", e);
        }
        return pojo;
    }

    public static Try<String> stringify(Object object) {
        return Try.of(() -> mapper.writeValueAsString(object));
    }

    public static <T> Try<T> parse(String json, Class<T> valueType) {
        return Try.of(() -> mapper.readValue(json, valueType));
    }

    public static <T> Try<T> parse(String json, TypeReference<T> valueTypeRef) {
        return Try.of(() -> mapper.readValue(json, valueTypeRef));
    }

    public static <T> Try<T> parse(byte[] bytes, Class<T> valueType) {
        return Try.of(() -> mapper.readValue(bytes, valueType));
    }

    public static <T> Try<T> parse(byte[] bytes, TypeReference<T> valueTypeRef) {
        return Try.of(() -> mapper.readValue(bytes, valueTypeRef));
    }

}

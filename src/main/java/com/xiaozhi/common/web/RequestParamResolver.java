package com.xiaozhi.common.web;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.interceptor.QueryParam;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

@Component
public class RequestParamResolver implements HandlerMethodArgumentResolver {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterAnnotation(QueryParam.class) != null
                || parameter.getParameterAnnotation(Authorized.class) != null;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {

        var authorizedAnnotation = parameter.getParameterAnnotation(Authorized.class);
        if (authorizedAnnotation != null) {
            return RequestContext.getUser();
        }

        var targetClass = parameter.getParameterType();
        var target = targetClass.getConstructor().newInstance();
        var wrapper = PropertyAccessorFactory.forBeanPropertyAccess(target);

        var fields = targetClass.getDeclaredFields();
        var superFields = targetClass.getSuperclass().getDeclaredFields();
        var allFields = new ArrayList<Field>() {
            {
                addAll(Arrays.asList(fields));
                addAll(Arrays.asList(superFields));
            }
        };

        for (Field field : allFields) {
            var paramName = this.camelToSnake(field.getName());

            var value = webRequest.getParameter(paramName);
            if (value != null) {
                if (field.getType() == Optional.class) {
                    wrapper.setPropertyValue(field.getName(), Optional.ofNullable(value));
                    continue;
                }
                if (field.getType() == List.class) {
                    var values = webRequest.getParameterValues(paramName);
                    if (values == null) {
                        values = new String[]{};
                    }
                    var type = field.getGenericType();
                    if (type instanceof ParameterizedType pt) {
                        for (var arg : pt.getActualTypeArguments()) {

                            wrapper.setPropertyValue(field.getName(), List.of(values));

                        }
                        continue;
                    }
                }
                wrapper.setPropertyValue(field.getName(), value);
            }
        }

        return target;
    }

    public String camelToSnake(String str) {
        // Regular Expression
        String regex = "([a-z])([A-Z]+)";

        // Replacement string
        String replacement = "$1_$2";

        // Replace the given regex
        // with replacement string
        // and convert it to lower case.
        str = str.replaceAll(regex, replacement).toLowerCase();

        // return string
        return str;
    }

    private LocalDateTime parseDate(String date, boolean start) {
        var onlyDate = Pattern.compile("\\d{4}-\\d{2}-\\d{2}")
                .matcher(date)
                .matches();
        if (onlyDate) {
            var localDate = LocalDate.parse(date);
            if (start) return localDate.atStartOfDay();
            // return LocalTime.MAX.atDate(localDate);
            return LocalTime.of(23, 59, 59).atDate(localDate);
        }
        return LocalDateTime.parse(date, formatter);
    }
}

package com.xiaozhi.common.web;

import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import io.vavr.control.Either;

@RestControllerAdvice
public class RespAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
                                  ServerHttpResponse response) {

        if (body == null) return Resp.fail(BizError.BadRequest);

        return switch (body) {
            case Resp resp -> resp;
            case Either<?, ?> either -> handleEither(either);
            default -> body;
        };
    }

    /**
     * 处理 Either 类型的响应
     * 支持 ErrorCode 和其他实现了 ErrorInfo 接口的错误类型
     */
    private Object handleEither(Either<?, ?> either) {
        return either.fold(
                left -> {
                    // 如果左侧实现了 ErrorInfo 接口（包括 ErrorCode）
                    if (left instanceof ErrorInfo errorInfo) {
                        return Resp.fail(errorInfo);
                    }
                    // 如果左侧是其他类型，使用通用错误
                    return Resp.fail(BizError.SystemError);
                },
                Resp::succeed
        );
    }
}

package com.xiaozhi.common.web;

import com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper;
import com.baomidou.mybatisplus.core.conditions.SharedString;
import com.baomidou.mybatisplus.core.conditions.query.Query;
import com.baomidou.mybatisplus.core.conditions.segments.MergeSegments;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;

public class OhMyLambdaQueryWrapper<T> extends AbstractLambdaWrapper<T, OhMyLambdaQueryWrapper<T>> implements Query<OhMyLambdaQueryWrapper<T>, T, SFunction<T, ?>> {

    private SharedString sqlSelect;

    public OhMyLambdaQueryWrapper() {
        this((T)null);
    }

    public OhMyLambdaQueryWrapper(T entity) {
        this.sqlSelect = new SharedString();
        super.setEntity(entity);
        super.initNeed();
    }

    public OhMyLambdaQueryWrapper(Class<T> entityClass) {
        this.sqlSelect = new SharedString();
        super.setEntityClass(entityClass);
        super.initNeed();
    }

    OhMyLambdaQueryWrapper(T entity, Class<T> entityClass, SharedString sqlSelect, AtomicInteger paramNameSeq, Map<String, Object> paramNameValuePairs, MergeSegments mergeSegments, SharedString paramAlias, SharedString lastSql, SharedString sqlComment, SharedString sqlFirst) {
        this.sqlSelect = new SharedString();
        super.setEntity(entity);
        super.setEntityClass(entityClass);
        this.paramNameSeq = paramNameSeq;
        this.paramNameValuePairs = paramNameValuePairs;
        this.expression = mergeSegments;
        this.sqlSelect = sqlSelect;
        this.paramAlias = paramAlias;
        this.lastSql = lastSql;
        this.sqlComment = sqlComment;
        this.sqlFirst = sqlFirst;
    }


    public OhMyLambdaQueryWrapper<T> select(boolean condition, List<SFunction<T, ?>> columns) {
        return this.doSelect(condition, columns);
    }

    public OhMyLambdaQueryWrapper<T> select(Class<T> entityClass, Predicate<TableFieldInfo> predicate) {
        if (entityClass == null) {
            entityClass = this.getEntityClass();
        } else {
            this.setEntityClass(entityClass);
        }

        Assert.notNull(entityClass, "entityClass can not be null", new Object[0]);
        this.sqlSelect.setStringValue(TableInfoHelper.getTableInfo(entityClass).chooseSelect(predicate));
        return this.typedThis;
    }

    @SafeVarargs
    public final OhMyLambdaQueryWrapper<T> select(SFunction<T, ?>... columns) {
        return this.doSelect(true, CollectionUtils.toList(columns));
    }

    @SafeVarargs
    public final OhMyLambdaQueryWrapper<T> select(boolean condition, SFunction<T, ?>... columns) {
        return this.doSelect(condition, CollectionUtils.toList(columns));
    }

    protected OhMyLambdaQueryWrapper<T> doSelect(boolean condition, List<SFunction<T, ?>> columns) {
        if (condition && CollectionUtils.isNotEmpty(columns)) {
            this.sqlSelect.setStringValue(this.columnsToString(false, columns));
        }

        return this.typedThis;
    }


    @SafeVarargs
    public final OhMyLambdaQueryWrapper<T> when(boolean isTrue, Object val, SFunction<T, ?>... columns) {
        if (!isTrue) return this;
        this.and(q -> {
            for (SFunction<T, ?> column : columns) {
                q.eq(column, val).or();
            }
        });
        return this;
    }

    @SafeVarargs
    public final OhMyLambdaQueryWrapper<T> multiLike(Object val, SFunction<T, ?>... columns) {
        return switch (val) {
            case Optional<?> opt -> opt.map(value -> {
                this.and(q -> {
                    for (SFunction<T, ?> column : columns) {
                        q.like(column, value).or();
                    }
                });
                return this;
            }).orElse(this);
            default -> this;
        };
    }

    @SafeVarargs
    public final OhMyLambdaQueryWrapper<T> multiEq(Object val, SFunction<T, ?>... columns) {
        for (SFunction<T, ?> column : columns) {
            this.or().eq(column, val);
        }
        return this;
    }

    public OhMyLambdaQueryWrapper<T> ne(SFunction<T, ?> column, Object val) {
        return switch (val) {
            case Optional<?> opt -> opt.map(value -> {
                super.ne(column, value);
                return this;
            }).orElse(this);
            default -> this.ne(column, Optional.of(val));
        };
    }

    @Override
    public OhMyLambdaQueryWrapper<T> eq(SFunction<T, ?> column, Object val) {
        return switch (val) {
            case Optional<?> opt -> opt.map(value -> super.eq(column, value)).orElse(this);
            default -> super.eq(column, val);
        };
    }

    @Override
    public OhMyLambdaQueryWrapper<T> like(SFunction<T, ?> column, Object val) {
        return switch (val) {
            case Optional<?> opt -> opt.map(value -> super.like(column, value)).orElse(this);
            default -> super.like(column, val);
        };
    }

    public String getSqlSelect() {
        return this.sqlSelect.getStringValue();
    }

    protected OhMyLambdaQueryWrapper<T> instance() {
        return new OhMyLambdaQueryWrapper<T>(this.getEntity(), this.getEntityClass(), null, this.paramNameSeq, this.paramNameValuePairs, new MergeSegments(), this.paramAlias, SharedString.emptyString(), SharedString.emptyString(), SharedString.emptyString());
    }

    public void clear() {
        super.clear();
        this.sqlSelect.toNull();
    }
}

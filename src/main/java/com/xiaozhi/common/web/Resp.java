package com.xiaozhi.common.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.vavr.control.Either;

import java.util.List;

/**
 * API 结果封装
 */
public sealed interface Resp permits Resp.Error, Resp.PagerResult, Resp.Result {

    /**
     * 成功
     */
    record Result<T>(int code, String msg, T data) implements Resp {
        private Result(T data) {
            this(0, "success", data);
        }
    }

    /**
     * 错误
     */
    record Error(int code, String msg) implements Resp {
        private Error(ErrorInfo errorInfo) {
            this(errorInfo.getCode(), errorInfo.getMsg());
        }
    }

    /**
     * 分页
     */
    record PagerResult<T extends List<?>>(int code, String msg, long total, T data) implements Resp {
        private PagerResult(T items) {
            this(0, "success", items.size(), items);
        }

        private PagerResult(T items, long total) {
            this(0, "success", total, items);
        }
    }

    static <T> Resp succeed(T data) {
        return switch (data) {
            // maybe throw exception
            case Error err -> err;
            case Result<?> result -> result;
            case PagerResult<?> pager -> pager;
            default -> new Result<>(data);
        };
    }

    static Error fail(ErrorInfo errorInfo) {
        return new Error(errorInfo);
    }

    static Error fail(int code, String msg) {
        return new Error(code, msg);
    }

    static <T> Resp from(Either<ErrorInfo, T> either) {
        return either.fold(Resp::fail, Resp::succeed);
    }

    static <T extends List<?>> Resp of(T items, long total) {
        return new PagerResult<>(items, total);
    }

    static <T> Resp from(IPage<T> page) {
        return new PagerResult<>(page.getRecords(), page.getTotal());
    }
}

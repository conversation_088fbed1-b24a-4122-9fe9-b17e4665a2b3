package com.xiaozhi.common.web;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;

public class RespJsonSerializer extends StdSerializer<Resp> {

    public RespJsonSerializer() {
        this(null);
    }
    public RespJsonSerializer(Class<Resp> t) {
        super(t);
    }

    @Override
    public void serialize(Resp resp, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        switch (resp) {
            case Resp.Error error -> {
                jsonGenerator.writeStartObject();
                jsonGenerator.writeNumberField("code", error.code());
                jsonGenerator.writeStringField("msg", error.msg());
                jsonGenerator.writeEndObject();
            }
            case Resp.Result<?> result -> {
                jsonGenerator.writeStartObject();
                jsonGenerator.writeNumberField("code", result.code());
                jsonGenerator.writeStringField("msg", result.msg());
                jsonGenerator.writeObjectField("results", result.data());
                jsonGenerator.writeEndObject();
            }
            case Resp.PagerResult<?> pager -> {
                jsonGenerator.writeStartObject();
                jsonGenerator.writeNumberField("code", pager.code());
                jsonGenerator.writeStringField("msg", pager.msg());
                jsonGenerator.writeNumberField("total", pager.total());
                jsonGenerator.writeObjectField("results", pager.data());
                jsonGenerator.writeEndObject();
            }
        }
    }
}

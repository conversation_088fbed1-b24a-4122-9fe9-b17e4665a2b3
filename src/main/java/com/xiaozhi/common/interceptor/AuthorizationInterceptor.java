package com.xiaozhi.common.interceptor;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.RequestContext;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.AuthService;
import com.xiaozhi.utils.JsonUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;

/**
 * AuthorizationInterceptor
 */
@Component
public class AuthorizationInterceptor implements HandlerInterceptor {

    @Autowired
    private AuthService authService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (handler instanceof HandlerMethod method) {
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/json; charset=utf-8");

            var passAuthAnnotation = method.getMethodAnnotation(PassAuth.class);
            if (passAuthAnnotation == null) {
                var token = request.getHeader("Authorization");

                if (Strings.isBlank(token)) {
                    try (var writer = response.getWriter()) {
                        JsonUtil.stringify(Resp.fail(BizError.Unauthorized))
                                .forEach(writer::write);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    return false;
                }

                return authService.verify(token)
                        .fold(err -> {
                            try (var writer = response.getWriter()) {
                                JsonUtil.stringify(Resp.fail(err)).forEach(writer::write);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            return false;
                        }, user -> {
                            RequestContext.setUser(user);
                            return true;
                        });
            }
        }

        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
            ModelAndView modelAndView) throws Exception {
        // 清理请求上下文
        RequestContext.clear();
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }
}

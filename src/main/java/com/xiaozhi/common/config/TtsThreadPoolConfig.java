package com.xiaozhi.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * TTS 专用线程池配置
 */
@Configuration
public class TtsThreadPoolConfig {
    
    /**
     * TTS 任务专用线程池
     */
    @Bean("ttsExecutor")
    public Executor ttsExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }
    
    /**
     * 音频处理专用线程池
     */
    @Bean("audioProcessingExecutor") 
    public Executor audioProcessingExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("audio-processing-");
        executor.initialize();
        return executor;
    }
}

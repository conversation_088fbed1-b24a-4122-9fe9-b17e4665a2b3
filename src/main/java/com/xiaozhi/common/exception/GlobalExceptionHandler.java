package com.xiaozhi.common.exception;

import com.xiaozhi.common.web.Resp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 静态资源找不到异常
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public Resp handleNoResourceFoundException(NoResourceFoundException e, WebRequest request) {
        logger.warn("静态资源找不到: {}", e.getResourcePath());
        return Resp.fail(HttpStatus.NOT_FOUND.value(), "请求的资源不存在");
    }

    /**
     * 业务异常处理
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Object> handleRuntimeException(RuntimeException e, WebRequest request) {
        String errorMessage = getErrorMessage(e);
        logger.error("业务异常: {}", errorMessage, e);
        var res = Resp.fail(500, "操作失败：" + errorMessage);
        return new ResponseEntity<>(res, HttpStatus.OK);
    }

    /**
     * 系统异常 - 作为最后的兜底处理
     */
    @ExceptionHandler(Exception.class)
    public Resp handleException(Exception e, WebRequest request) {
        String errorMessage = getErrorMessage(e);
        logger.error("系统异常: {}", errorMessage, e);
        return Resp.fail(500, "服务器错误，请联系管理员");
    }

    /**
     * 安全地获取异常消息，避免类型转换错误
     */
    private String getErrorMessage(Exception e) {
        try {
            String message = e.getMessage();
            if (message == null) {
                return "未知错误";
            }
            // 如果消息是字符串，直接返回
            return message;
        } catch (ClassCastException cce) {
            // 如果getMessage()抛出ClassCastException，说明message字段不是String类型
            logger.warn("异常消息类型转换错误，使用异常类名作为消息: {}", e.getClass().getSimpleName());
            return "异常类型: " + e.getClass().getSimpleName();
        } catch (Exception ex) {
            // 其他获取消息时的异常
            logger.warn("获取异常消息时发生错误: {}", ex.getMessage());
            return "获取错误消息失败: " + e.getClass().getSimpleName();
        }
    }
}

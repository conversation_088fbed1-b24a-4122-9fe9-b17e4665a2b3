package com.xiaozhi.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * MCP endpoint DTO for frontend communication
 */
@Data
public class McpEndpointDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * Endpoint URL
     */
    private String url;
    
    /**
     * Endpoint name
     */
    private String name;
    
    /**
     * Authentication token (if required)
     */
    private String token;
    
    /**
     * Other headers
     */
    private Map<String, String> headers;
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;
}
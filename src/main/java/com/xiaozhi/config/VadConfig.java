package com.xiaozhi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * VAD (Voice Activity Detection) 配置类
 * 管理语音活动检测相关配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "vad")
public class VadConfig {

    /**
     * 模型配置
     */
    private Model model = new Model();

    /**
     * 预缓冲配置
     */
    private int prebufferMs = 200;

    /**
     * 能量阈值
     */
    private float energyThreshold = 0.5f;

    /**
     * 语音阈值
     */
    private float speechThreshold = 0.5f;

    /**
     * 静音阈值
     */
    private float silenceThreshold = 0.5f;

    /**
     * 静音持续时间（毫秒）
     */
    private int silenceMs = 1000;

    @Data
    public static class Model {
        /**
         * VAD 模型文件路径
         */
        private String path = "models/silero_vad.onnx";

        /**
         * 模型类型
         */
        private String type = "silero";

        /**
         * 采样率
         */
        private int sampleRate = 16000;

        /**
         * 窗口大小
         */
        private int windowSize = 512;
    }
}

package com.xiaozhi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 邮件配置类
 * 管理邮件服务相关配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "email.smtp")
public class EmailConfig {

    /**
     * SMTP 用户名
     */
    private String username;

    /**
     * SMTP 密码
     */
    private String password;

    /**
     * SMTP 服务器地址
     */
    private String host = "smtp.gmail.com";

    /**
     * SMTP 服务器端口
     */
    private int port = 587;

    /**
     * 是否启用 TLS
     */
    private boolean enableTls = true;

    /**
     * 是否启用认证
     */
    private boolean enableAuth = true;
}

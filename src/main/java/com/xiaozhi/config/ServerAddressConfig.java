package com.xiaozhi.config;

import com.xiaozhi.communication.server.websocket.WebSocketConfig;
import com.xiaozhi.utils.CmsUtils;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 服务器地址配置类
 * 统一管理 WebSocket、OTA、HTTP、MQTT 服务器地址
 */
@Data
@Component
@ConfigurationProperties(prefix = "xiaozhi.server")
public class ServerAddressConfig {

    private static final Logger logger = LoggerFactory.getLogger(ServerAddressConfig.class);

    @Autowired
    private ServerProperties serverProperties;

    /**
     * 服务器地址配置
     */
    private Address address = new Address();

    /**
     * WebSocket 配置
     */
    private WebSocket websocket = new WebSocket();

    /**
     * MQTT 配置
     */
    private Mqtt mqtt = new Mqtt();

    // 缓存的完整地址
    private String websocketAddress;
    private String otaAddress;
    private String serverAddress;
    private String mqttAddress;

    private String serverIp;

    @PostConstruct
    private void initializeAddresses() {
        serverIp = getEffectiveServerIp();
        var port = getEffectivePort();
        
        // 构建完整地址
        websocketAddress = buildWebSocketAddress(serverIp, port);
        serverAddress = buildServerAddress(serverIp, port);
        otaAddress = serverAddress + serverProperties.getServlet().getContextPath();
        mqttAddress = String.format("%s:%d", serverIp, mqtt.getPort());
        
        logger.info("服务器地址初始化完成:");
        logger.info("  WebSocket: {}", websocketAddress);
        logger.info("  OTA: {}", otaAddress);
        logger.info("  MQTT: {}", mqttAddress);
        logger.info("  Server: {}", serverAddress);
        logger.info("  Swagger: {}/swagger-ui.html", serverAddress);
    }

    /**
     * 获取有效的服务器IP
     */
    private String getEffectiveServerIp() {
        // 优先使用配置文件中的IP
        if (address.getHost() != null && !address.getHost().trim().isEmpty()) {
            return address.getHost();
        }
        
        // 其次使用环境变量
        String envHost = System.getenv("SERVER_HOST");
        if (envHost != null && !envHost.trim().isEmpty()) {
            return envHost;
        }
        
        // 最后使用自动检测的IP
        return CmsUtils.getServerIp();
    }

    /**
     * 获取有效的端口
     */
    private Integer getEffectivePort() {
        // 优先使用配置文件中的端口
        if (address.getPort() != null && address.getPort() > 0) {
            return address.getPort();
        }
        
        // 其次使用环境变量
        String envPort = System.getenv("SERVER_PORT");
        if (envPort != null && !envPort.trim().isEmpty()) {
            try {
                return Integer.parseInt(envPort);
            } catch (NumberFormatException e) {
                logger.warn("环境变量 SERVER_PORT 格式错误: {}", envPort);
            }
        }
        
        // 最后使用 Spring Boot 默认端口
        return serverProperties.getPort();
    }

    /**
     * 构建 WebSocket 地址
     */
    private String buildWebSocketAddress(String host, Integer port) {
        String protocol = websocket.getSsl() ? "wss" : "ws";
        String path = websocket.getPath() != null ? websocket.getPath() : WebSocketConfig.WS_PATH;
        return String.format("%s://%s:%d%s", protocol, host, port, path);
    }

    /**
     * 构建服务器地址
     */
    private String buildServerAddress(String host, Integer port) {
        String protocol = address.getSsl() ? "https" : "http";
        return String.format("%s://%s:%d", protocol, host, port);
    }

    /**
     * 地址配置
     */
    @Data
    public static class Address {
        /**
         * 服务器主机地址（可选，不配置则自动检测）
         */
        private String host;

        /**
         * 服务器端口（可选，不配置则使用 server.port）
         */
        private Integer port;

        /**
         * 是否启用 SSL
         */
        private Boolean ssl = false;
    }

    /**
     * WebSocket 配置
     */
    @Data
    public static class WebSocket {
        /**
         * 服务器主机地址
         */
        private String host;

        /**
         * 服务器端口
         */
        private int port;

        /**
         * WebSocket 路径（可选，不配置则使用默认路径）
         */
        private String path;

        /**
         * 是否启用 SSL
         */
        private Boolean ssl = false;
    }

    /**
     * OTA 配置
     */
    @Data
    public static class Mqtt {
        /**
         * 服务器主机地址
         */
        private String host;

        /**
         * 服务器端口
         */
        private int port;

        /**
         * 用户名
         */
        private String username;

        /**
         * 密码
         */
        private String password;

        /**
         * 客户端发布主题
         */
        private String clientPublishTopic;

        /**
         * 服务端订阅主题
         */
        private String serverSubscribeTopic;
    }
}

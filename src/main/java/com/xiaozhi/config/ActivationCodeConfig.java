package com.xiaozhi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 激活码配置类
 * 管理激活码相关配置，支持选择 MySQL 或 Redis 实现方式
 */
@Data
@Component
@ConfigurationProperties(prefix = "xiaozhi.activation-code")
public class ActivationCodeConfig {

    /**
     * 激活码存储类型：mysql 或 redis
     */
    private StorageType storageType = StorageType.MYSQL;

    /**
     * 激活码有效期（小时），默认24小时（当天有效）
     */
    private int validityHours = 24;

    /**
     * 激活码长度，默认6位
     */
    private int codeLength = 6;

    /**
     * 激活码前缀（可选）
     */
    private String codePrefix = "";

    /**
     * Redis 相关配置
     */
    private Redis redis = new Redis();

    /**
     * 存储类型枚举
     */
    public enum StorageType {
        MYSQL, REDIS
    }

    /**
     * Redis 配置
     */
    @Data
    public static class Redis {
        /**
         * Redis key 前缀
         */
        private String keyPrefix = "xiaozhi:activation:";

        /**
         * 是否启用 Redis 集群模式
         */
        private boolean clusterEnabled = false;
    }
}

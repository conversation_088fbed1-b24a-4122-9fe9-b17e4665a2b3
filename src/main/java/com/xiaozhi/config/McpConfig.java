package com.xiaozhi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MCP (Model Context Protocol) 配置类
 * 管理 MCP 设备相关配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "xiaozhi.mcp")
public class McpConfig {

    /**
     * 设备配置
     */
    private Device device = new Device();

    /**
     * 连接配置
     */
    private Connection connection = new Connection();

    @Data
    public static class Device {
        /**
         * 最大工具数量
         */
        private int maxToolsCount = 32;

        /**
         * 设备超时时间（毫秒）
         */
        private long timeoutMs = 30000;

        /**
         * 是否启用设备自动发现
         */
        private boolean autoDiscovery = true;
    }

    @Data
    public static class Connection {
        /**
         * 连接超时时间（毫秒）
         */
        private long timeoutMs = 10000;

        /**
         * 重试次数
         */
        private int retryCount = 3;

        /**
         * 重试间隔（毫秒）
         */
        private long retryIntervalMs = 1000;
    }
}

package com.xiaozhi.enums;

/**
 * 音频生成状态枚举
 * 用于跟踪TTS音频生成的不同阶段
 */
public enum AudioGenerationStatus {
    
    /**
     * 等待生成 - 句子已创建但尚未开始音频生成
     */
    PENDING("等待生成"),
    
    /**
     * 生成中 - 正在进行TTS音频生成
     */
    GENERATING("生成中"),
    
    /**
     * 生成完成 - 音频生成成功完成
     */
    COMPLETED("生成完成"),
    
    /**
     * 生成失败 - 音频生成过程中发生错误
     */
    FAILED("生成失败");
    
    private final String description;
    
    AudioGenerationStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 判断是否为终态（已完成或失败）
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED;
    }
    
    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == COMPLETED;
    }
    
    /**
     * 判断是否为进行中状态
     */
    public boolean isInProgress() {
        return this == GENERATING;
    }
}

package com.xiaozhi.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum UserStatus {
    Normal(1, "正常"),
    Frozen(2, "冻结");

    @EnumValue
    @JsonValue
    private final int value;
    private final String text;

    UserStatus(int value, String text) {
        this.value = value;
        this.text = text;
    }
}

package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.SysRoleService;
import com.xiaozhi.vo.RoleCreateParams;
import com.xiaozhi.vo.RoleQueryParams;
import com.xiaozhi.vo.RoleUpdateParams;
import com.xiaozhi.vo.TestVoiceParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 角色管理
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/roles")
@Tag(name = "角色管理", description = "角色管理")
public class RoleController {

    @Resource
    private SysRoleService roleService;

    @GetMapping()
    @Operation(summary = "角色列表")
    public Resp find(@Authorized AuthorizedUser user, RoleQueryParams params) {
        params.setUserId(Optional.of(user.getId()));
        return roleService.findPage(params);
    }

    @PostMapping()
    @Operation(summary = "添加角色")
    public Either<BizError, ?> add(@Authorized AuthorizedUser user, @RequestBody RoleCreateParams params) {
        params.setUserId(user.getId());
        return roleService.create(params);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新角色")
    public Either<BizError, ?> update(@PathVariable Integer id, @RequestBody RoleUpdateParams params) {
        return roleService.update(id, params);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除角色")
    public Either<BizError, ?> delete(@PathVariable Integer id) {
        return roleService.delete(id);
    }

    @PostMapping("/test-voice")
    @Operation(summary = "测试语音")
    public Either<BizError, ?> testVoice(@RequestBody TestVoiceParams params) {
        return roleService.testVoice(params);
    }

}
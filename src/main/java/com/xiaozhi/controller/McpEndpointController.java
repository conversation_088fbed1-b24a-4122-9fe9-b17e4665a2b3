package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.PassAuth;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dto.McpEndpointDto;
import com.xiaozhi.service.SysMcpEndpointService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * MCP Endpoint Controller
 */
@RestController
@RequestMapping("mcp-endpoints")
@Tag(name = "McpEndpoint", description = "MCP端点管理")
public class McpEndpointController {

    @Resource
    private SysMcpEndpointService mcpEndpointService;

    // ==================== CRUD Operations ====================

    @GetMapping
    @Operation(summary = "获取MCP端点列表")
    public Resp find() {
        return mcpEndpointService.findPage();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取MCP端点详情", description = "根据ID获取MCP端点详情")
    public Resp detail(@PathVariable Integer id) {
        return Resp.succeed(true);
    }

    @PassAuth
    @PostMapping
    @Operation(summary = "添加MCP端点")
    public Either<BizError, ?> add(@RequestBody McpEndpointDto dto) {
        return mcpEndpointService.create(dto);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新MCP端点")
    public Either<BizError, ?> update(@PathVariable Integer id, @RequestBody McpEndpointDto dto) {
        return mcpEndpointService.update(id, dto);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除MCP端点")
    public Either<BizError, ?> delete(@PathVariable Integer id) {
        return mcpEndpointService.delete(id);
    }
}

package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.interceptor.QueryParam;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.entity.SysAgent;
import com.xiaozhi.service.SysAgentService;
import com.xiaozhi.vo.AgentCreateParams;
import com.xiaozhi.vo.AgentQueryParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 智能体管理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agents")
@Tag(name = "智能体管理", description = "智能体管理")
public class AgentController {

    @Resource
    private SysAgentService agentService;

    @GetMapping()
    @Operation(summary = "智能体列表")
    public Resp find(@QueryParam AgentQueryParams params) {
        return agentService.findPage(params);
    }

    @PostMapping()
    @Operation(summary = "添加智能体")
    public Either<BizError, ?> add(@Authorized AuthorizedUser user, @RequestBody AgentCreateParams params) {
        params.setUserId(user.getId());
        return agentService.create(params);
    }

    @PostMapping("{id}")
    @Operation(summary = "更新智能体")
    public Either<BizError, ?> update(@PathVariable Integer id, @RequestBody SysAgent params) {
        return agentService.update(id, params);
    }

    @DeleteMapping("{id}")
    @Operation(summary = "删除智能体")
    public Either<BizError, ?> delete(@PathVariable Integer id) {
        return agentService.delete(id);
    }
}
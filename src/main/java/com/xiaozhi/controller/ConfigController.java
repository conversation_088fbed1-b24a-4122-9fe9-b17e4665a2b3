package com.xiaozhi.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.interceptor.QueryParam;
import com.xiaozhi.common.web.AjaxResult;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.service.SysConfigService;
import com.xiaozhi.vo.ConfigQueryParams;
import com.xiaozhi.vo.ConfigUpdateParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

/**
 * 配置管理
 * 
 * <AUTHOR>
 * 
 */

@RestController
@RequestMapping("/configs")
@Tag(name = "配置管理", description = "配置管理")
public class ConfigController extends BaseController {

    @Resource
    private SysConfigService configService;

    @Resource
    private TtsServiceFactory ttsServiceFactory;

    @Resource
    private SttServiceFactory sttServiceFactory;

    @GetMapping()
    @Operation(summary = "配置查询")
    public Resp find(@QueryParam ConfigQueryParams params) {
        return configService.findPage(params);
    }

    @PutMapping("{id}")
    @Operation(summary = "更新配置")
    public Either<BizError, ?> update(@PathVariable Integer id, ConfigUpdateParams params) {
        return configService.update(id, params);
    }

    @PostMapping()
    @Operation(summary = "添加配置")
    public Either<BizError, ?> create(@Authorized AuthorizedUser user, SysConfig config) {
        config.setUserId(user.getId());
        return configService.create(config);
    }

    @DeleteMapping("{id}")
    @Operation(summary = "删除配置")
    public Either<BizError, ?> delete(@PathVariable Integer id) {
        return configService.delete(id);
    }

    @PostMapping("models")
    public AjaxResult getModels(SysConfig config) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + config.getApiKey());

            // 构建请求实体
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 调用 /v1/models 接口，解析为 JSON 字符串
            ResponseEntity<String> response = restTemplate.exchange(
                    config.getApiUrl() + "/models",
                    HttpMethod.GET,
                    entity,
                    String.class);

            // 使用 ObjectMapper 解析 JSON 响应
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(response.getBody());

            // 提取 "data" 字段
            JsonNode dataNode = rootNode.get("data");
            if (dataNode == null || !dataNode.isArray()) {
                return AjaxResult.error("响应数据格式错误，缺少 data 字段或 data 不是数组");
            }

            // 将 "data" 字段解析为 List<Map<String, Object>>
            List<Map<String, Object>> modelList = objectMapper.convertValue(
                    dataNode,
                    new TypeReference<List<Map<String, Object>>>() {
                    });

            // 返回成功结果
            AjaxResult result = AjaxResult.success();
            result.put("data", modelList);
            return result;

        } catch (HttpClientErrorException e) {
            // 捕获 HTTP 客户端异常并返回详细错误信息
            String errorMessage = e.getResponseBodyAsString();
            // 返回详细错误信息到前端
            return AjaxResult.error("调用模型接口失败: " + errorMessage);

        } catch (Exception e) {
            // 捕获其他异常并记录日志
            return AjaxResult.error();
        }
    }
}
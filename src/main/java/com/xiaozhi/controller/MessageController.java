package com.xiaozhi.controller;

import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.SysMessageService;
import com.xiaozhi.vo.MessageQueryParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("messages")
@Tag(name = "对话消息管理", description = "对话消息管理")
public class MessageController extends BaseController {

    @Resource
    private SysMessageService messageService;

    @GetMapping()
    @Operation(summary = "对话列表")
    public Resp find(MessageQueryParams params) {
        return messageService.findPage(params);
    }

}
package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.SysTemplateService;
import com.xiaozhi.vo.TemplateCreateParams;
import com.xiaozhi.vo.TemplateQueryParams;
import com.xiaozhi.vo.TemplateUpdateParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 模板管理
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/templates")
@Tag(name = "模板管理", description = "模板管理")
public class TemplateController {

    @Resource
    private SysTemplateService templateService;

    @GetMapping()
    @Operation(summary = "模板列表")
    public Resp find(@Authorized AuthorizedUser user, TemplateQueryParams params) {
        params.setUserId(Optional.of(user.getId()));
        return templateService.findPage(params);
    }

    @PostMapping()
    @Operation(summary = "添加模板")
    public Either<BizError, ?> add(@Authorized AuthorizedUser user, @RequestBody TemplateCreateParams params) {
        params.setUserId(user.getId());
        return templateService.create(params);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新模板")
    public Either<BizError, ?> update(@PathVariable Integer id, @RequestBody TemplateUpdateParams params) {
        return templateService.update(id, params);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除模板")
    public Either<BizError, ?> delete(@PathVariable Integer id) {
        return templateService.delete(id);
    }

}
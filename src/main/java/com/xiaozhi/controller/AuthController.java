package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.PassAuth;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.service.AuthService;
import com.xiaozhi.vo.UserLoginParams;
import io.swagger.v3.oas.annotations.Operation;
import io.vavr.control.Either;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    @PassAuth
    @PostMapping("/login")
    @Operation(summary = "管理端用户登录")
    public Either<BizError, ?> login(@RequestBody UserLoginParams params) {
        return authService.login(params);
    }

}

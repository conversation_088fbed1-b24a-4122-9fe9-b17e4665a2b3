package com.xiaozhi.service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 激活码服务接口
 * 提供激活码的生成、验证、清理等功能
 */
public interface ActivationService {

    /**
     * 为设备生成激活码
     * 
     * @param deviceId 设备ID
     * @param deviceType 设备类型
     * @return 生成的激活码
     */
    String generateActiveCode(String deviceId, String deviceType);

    /**
     * 根据设备ID查询激活码
     * 
     * @param deviceId 设备ID
     * @return 如果存在返回激活码信息，否则返回空
     */
    Optional<String> getActivationCodeByDeviceId(String deviceId);

    /**
     * 删除激活码（激活成功后调用）
     * 
     * @param code 激活码
     * @return 是否删除成功
     */
    boolean deleteActivationCode(String code);

    /**
     * 清理过期的激活码
     * 
     * @return 清理的激活码数量
     */
    int cleanExpiredCodes();

    /**
     * 检查激活码是否存在
     * 
     * @param code 激活码
     * @return 是否存在
     */
    boolean exists(String code);

    /**
     * 激活码信息
     */
    class ActivationCodeInfo {
        private String code;
        private String deviceId;
        private String deviceType;
        private LocalDateTime createdAt;
        private LocalDateTime expiresAt;

        public ActivationCodeInfo() {}

        public ActivationCodeInfo(String code, String deviceId, String deviceType, 
                                LocalDateTime createdAt, LocalDateTime expiresAt) {
            this.code = code;
            this.deviceId = deviceId;
            this.deviceType = deviceType;
            this.createdAt = createdAt;
            this.expiresAt = expiresAt;
        }

        // Getters and Setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }

        public String getDeviceId() { return deviceId; }
        public void setDeviceId(String deviceId) { this.deviceId = deviceId; }

        public String getDeviceType() { return deviceType; }
        public void setDeviceType(String deviceType) { this.deviceType = deviceType; }

        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

        public LocalDateTime getExpiresAt() { return expiresAt; }
        public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }

        /**
         * 检查激活码是否已过期
         */
        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expiresAt);
        }
    }
}

package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.vo.DeviceCreateParams;
import com.xiaozhi.vo.DeviceQueryParams;
import io.vavr.control.Either;

/**
 * 设备查询/更新
 * 
 * <AUTHOR>
 * 
 */
public interface SysDeviceService {

  /**
   * 添加设备
   */
  Either<BizError, ?> add(DeviceCreateParams params);

  /**
   * 更新设备
   */
  Either<BizError,?> update(Integer id, SysDevice device);

  /**
   * 删除设备
   */
  Either<BizError,?> delete(Integer id);

  /**
   * 设备列表
   */
  Resp findPage(DeviceQueryParams params, Integer userId);

  /**
   * 查询设备信息，并join配置表联查，用来过滤不存在的configId
   */
  SysDevice findByDeviceId(String deviceId);

  /**
   * 查询并生成验证码
   */
  SysDevice generateCode(SysDevice device);

  /**
   * 关系设备验证码语音路径
   */
  int updateCode(SysDevice device);

  /**
   * 直接插入设备记录（用于OTA时保存未绑定设备）
   */
  int insertDevice(SysDevice device);
}
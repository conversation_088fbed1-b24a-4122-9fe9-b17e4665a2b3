package com.xiaozhi.service;

import com.xiaozhi.common.web.Resp;
import com.xiaozhi.entity.SysMessage;
import com.xiaozhi.vo.MessageQueryParams;

import java.util.List;

/**
 * 聊天记录查询/添加
 * 
 * <AUTHOR>
 * 
 */
public interface SysMessageService {

  Resp findPage(MessageQueryParams params);

  List<SysMessage> findOf(String deviceId, String messageType, Integer limit);

  void save(SysMessage message);
}
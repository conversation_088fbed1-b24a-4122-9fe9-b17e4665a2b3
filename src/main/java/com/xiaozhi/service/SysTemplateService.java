package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.entity.SysTemplate;
import com.xiaozhi.vo.TemplateCreateParams;
import com.xiaozhi.vo.TemplateQueryParams;
import com.xiaozhi.vo.TemplateUpdateParams;
import io.vavr.control.Either;

import java.util.List;

/**
 * 提示词模板服务接口
 */
public interface SysTemplateService {
    
    /**
     * 添加模板
     * 
     * @param template 模板信息
     * @return 结果
     */
    int add(SysTemplate template);
    
    /**
     * 修改模板
     * 
     * @param template 模板信息
     * @return 结果
     */
    int update(SysTemplate template);
    
    /**
     * 删除模板 - 旧方法
     *
     * @param templateId 模板ID
     * @return 结果
     */
    int deleteTemplate(Integer templateId);
    
    /**
     * 查询模板列表
     * 
     * @param template 模板信息
     * @return 模板集合
     */
    List<SysTemplate> query(SysTemplate template);
    
    /**
     * 查询模板详情
     *
     * @param templateId 模板ID
     * @return 模板信息
     */
    SysTemplate selectTemplateById(Integer templateId);

    // ========== 新的 DeviceController 风格方法 ==========

    /**
     * 添加模板
     */
    Either<BizError, ?> create(TemplateCreateParams params);

    /**
     * 更新模板
     */
    Either<BizError, ?> update(Integer id, TemplateUpdateParams params);

    /**
     * 删除模板
     */
    Either<BizError, ?> delete(Integer id);

    /**
     * 模板列表
     */
    Resp findPage(TemplateQueryParams params);

}
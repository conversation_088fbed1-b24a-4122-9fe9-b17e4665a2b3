package com.xiaozhi.service.impl;

import com.xiaozhi.config.ActivationCodeConfig;
import com.xiaozhi.service.ActivationService;
import com.xiaozhi.utils.CommonUtils;
import com.xiaozhi.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.Set;

/**
 * Redis 版本的激活码服务实现
 * 基于 Redis 实现激活码的生成、验证、清理等功能，使用 TTL 控制当天有效期
 */
@Slf4j
@Service("redisActivationCodeService")
public class RedisActivationServiceImpl implements ActivationService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ActivationCodeConfig activationCodeConfig;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取激活码的 Redis key
     */
    private String getCodeKey(String code) {
        return activationCodeConfig.getRedis().getKeyPrefix() + "code:" + code;
    }

    /**
     * 获取设备ID的 Redis key
     */
    private String getDeviceKey(String deviceId) {
        return activationCodeConfig.getRedis().getKeyPrefix() + "device:" + deviceId;
    }

    @Override
    public String generateActiveCode(String deviceId, String deviceType) {
        // 检查是否已存在未过期的激活码
        var existingCode = getActivationCodeByDeviceId(deviceId);
        if (existingCode.isPresent()) return existingCode.get();

        // 生成新的激活码
        String code;
        int maxRetries = 10;
        int retries = 0;
        
        do {
            code = activationCodeConfig.getCodePrefix() + CommonUtils.generateActivationCode();
            retries++;
        } while (exists(code) && retries < maxRetries);

        if (retries >= maxRetries) {
            throw new RuntimeException("无法生成唯一的激活码，请稍后重试");
        }

        // 创建激活码信息
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiresAt = now.plusHours(activationCodeConfig.getValidityHours());
        
        ActivationCodeInfo info = new ActivationCodeInfo(
            code, deviceId, deviceType, now, expiresAt
        );

        // 保存到 Redis，设置过期时间
        Duration ttl = Duration.ofHours(activationCodeConfig.getValidityHours());
        String infoJson = JsonUtil.toJson(info);
        
        // 保存激活码 -> 设备信息的映射
        stringRedisTemplate.opsForValue().set(getCodeKey(code), infoJson, ttl);
        
        // 保存设备ID -> 激活码的映射
        stringRedisTemplate.opsForValue().set(getDeviceKey(deviceId), code, ttl);
        
        log.info("为设备 {} 生成激活码: {}", deviceId, code);
        return code;
    }

    @Override
    public Optional<String> getActivationCodeByDeviceId(String deviceId) {
        String code = stringRedisTemplate.opsForValue().get(getDeviceKey(deviceId));
        if (code == null) {
            return Optional.empty();
        }

        return Optional.of(code);
    }

    @Override
    public boolean deleteActivationCode(String code) {
        try {
            // 先获取激活码信息以获取设备ID
            String infoJson = stringRedisTemplate.opsForValue().get(getCodeKey(code));
            if (infoJson != null) {
                ActivationCodeInfo info = JsonUtil.fromJson(infoJson, ActivationCodeInfo.class);
                // 删除设备ID -> 激活码的映射
                stringRedisTemplate.delete(getDeviceKey(info.getDeviceId()));
            }
            
            // 删除激活码 -> 设备信息的映射
            Boolean deleted = stringRedisTemplate.delete(getCodeKey(code));
            
            if (Boolean.TRUE.equals(deleted)) {
                log.info("删除激活码: {}", code);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除激活码失败: {}", code, e);
            return false;
        }
    }

    @Override
    public int cleanExpiredCodes() {
        try {
            // Redis 的 TTL 机制会自动清理过期的 key，这里主要是为了统计
            String pattern = activationCodeConfig.getRedis().getKeyPrefix() + "code:*";
            Set<String> keys = stringRedisTemplate.keys(pattern);
            
            if (keys == null || keys.isEmpty()) {
                return 0;
            }

            int expiredCount = 0;
            for (String key : keys) {
                String infoJson = stringRedisTemplate.opsForValue().get(key);
                if (infoJson != null) {
                    try {
                        ActivationCodeInfo info = JsonUtil.fromJson(infoJson, ActivationCodeInfo.class);
                        if (info.isExpired()) {
                            stringRedisTemplate.delete(key);
                            // 同时删除设备映射
                            stringRedisTemplate.delete(getDeviceKey(info.getDeviceId()));
                            expiredCount++;
                        }
                    } catch (Exception e) {
                        log.warn("解析激活码信息失败，删除无效key: {}", key);
                        stringRedisTemplate.delete(key);
                        expiredCount++;
                    }
                }
            }
            
            log.info("清理过期激活码，删除数量: {}", expiredCount);
            return expiredCount;
        } catch (Exception e) {
            log.error("清理过期激活码失败", e);
            return 0;
        }
    }

    @Override
    public boolean exists(String code) {
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(getCodeKey(code)));
    }
}

package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dao.TemplateMapper;
import com.xiaozhi.entity.SysTemplate;
import com.xiaozhi.service.SysTemplateService;
import com.xiaozhi.vo.TemplateCreateParams;
import com.xiaozhi.vo.TemplateQueryParams;
import com.xiaozhi.vo.TemplateUpdateParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 提示词模板服务实现类
 */
@Service
public class SysTemplateServiceImpl implements SysTemplateService {
    
    @Resource
    private TemplateMapper templateMapper;

    /**
     * 添加模板
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int add(SysTemplate template) {
        // 如果是默认模板，先重置其他默认模板
        if (template.getIsDefault() != null && template.getIsDefault().equals("1")) {
            templateMapper.resetDefault(template);
        }
        return templateMapper.add(template);
    }
    
    /**
     * 修改模板
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int update(SysTemplate template) {
        // 如果是默认模板，先重置其他默认模板
        if (template.getIsDefault() != null && template.getIsDefault().equals("1")) {
            templateMapper.resetDefault(template);
        }
        return templateMapper.update(template);
    }
    
    /**
     * 删除模板 - 旧方法
     */
    @Override
    public int deleteTemplate(Integer templateId) {
        return templateMapper.delete(templateId);
    }
    
    /**
     * 查询模板列表
     */
    @Override
    public List<SysTemplate> query(SysTemplate template) {
        return templateMapper.query(template);
    }
    
    /**
     * 查询模板详情
     */
    @Override
    public SysTemplate selectTemplateById(Integer templateId) {
        return templateMapper.selectTemplateById(templateId);
    }

    // ========== 新的 DeviceController 风格方法实现 ==========

    /**
     * 添加模板
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public Either<BizError, ?> create(TemplateCreateParams params) {
        return Option.of(params)
                .toEither(BizError.BadRequest)
                .map(p -> {
                    SysTemplate template = new SysTemplate();
                    template.setUserId(p.getUserId());
                    template.setName(p.getTemplateName());
                    template.setIntro(p.getTemplateDesc());
                    template.setContent(p.getTemplateContent());
                    template.setCategory(p.getCategory());
                    template.setIsDefault(p.getIsDefault());
                    template.setIsEnabled(true); // 默认启用

                    // 如果是默认模板，先重置其他默认模板
                    if (template.getIsDefault()) {
                        templateMapper.resetDefault(template);
                    }

                    int result = templateMapper.add(template);
                    if (result > 0) {
                        return Either.<BizError, Object>right(template);
                    } else {
                        return Either.<BizError, Object>left(BizError.SystemError);
                    }
                })
                .fold(Either::left, identity -> identity);
    }

    /**
     * 更新模板
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public Either<BizError, ?> update(Integer id, TemplateUpdateParams params) {
        return Option.of(templateMapper.selectTemplateById(id))
                .toEither(BizError.of(404, "模板不存在"))
                .map(template -> {
                    // 更新模板信息
                    if (params.getTemplateName() != null) template.setName(params.getTemplateName());
                    if (params.getTemplateDesc() != null) template.setIntro(params.getTemplateDesc());
                    if (params.getTemplateContent() != null) template.setContent(params.getTemplateContent());
                    if (params.getCategory() != null) template.setCategory(params.getCategory());
                    if (params.getIsDefault() != null) template.setIsDefault(params.getIsDefault());
                    if (params.getIsEnabled() != null) template.setIsEnabled(params.getIsEnabled());

                    // 如果是默认模板，先重置其他默认模板
                    if (template.getIsDefault()) {
                        templateMapper.resetDefault(template);
                    }

                    int result = templateMapper.update(template);
                    if (result > 0) {
                        return Either.<BizError, Object>right(template);
                    } else {
                        return Either.<BizError, Object>left(BizError.SystemError);
                    }
                })
                .fold(Either::left, identity -> identity);
    }

    /**
     * 删除模板
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public Either<BizError, ?> delete(Integer id) {
        return Option.of(templateMapper.selectTemplateById(id))
                .toEither(BizError.of(404, "模板不存在"))
                .flatMap(template -> {
                    // 软删除：设置状态为禁用
                    template.setIsEnabled(false);
                    int result = templateMapper.update(template);

                    if (result > 0) {
                        return Either.right("删除成功");
                    } else {
                        return Either.left(BizError.SystemError);
                    }
                });
    }

    /**
     * 模板列表
     */
    @Override
    public Resp findPage(TemplateQueryParams params) {
        var query = new OhMyLambdaQueryWrapper<SysTemplate>()
                .eq(SysTemplate::getUserId, params.getUserId())
                .eq(SysTemplate::getCategory, params.getCategory());

        var page = templateMapper.selectPage(params.toPage(), query);

        return Resp.from(page);
    }

}
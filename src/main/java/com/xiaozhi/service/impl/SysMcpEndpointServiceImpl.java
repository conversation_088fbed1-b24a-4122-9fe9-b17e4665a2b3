package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dao.SysMcpEndpointMapper;
import com.xiaozhi.dto.McpEndpointDto;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * MCP endpoint service implementation
 */
@Service
public class SysMcpEndpointServiceImpl implements SysMcpEndpointService {

    @Resource
    private SysMcpEndpointMapper mcpEndpointMapper;

    @Override
    public Resp findPage() {
        return null;
    }

    @Override
    public Either<BizError, ?> create(McpEndpointDto dto) {
        return null;
    }

    @Override
    public Either<BizError, ?> update(Integer id, McpEndpointDto dto) {
        return null;
    }

    @Override
    public Either<BizError, ?> delete(Integer id) {
        return null;
    }

    @Override
    public List<SysMcpEndpoint> findEndpoints() {
        var query = new OhMyLambdaQueryWrapper<SysMcpEndpoint>()
                .eq(SysMcpEndpoint::getIsEnabled, true)
                .select(SysMcpEndpoint::getId, SysMcpEndpoint::getName, SysMcpEndpoint::getUrl, SysMcpEndpoint::getHeaders);

        return mcpEndpointMapper.selectList(query);
    }
}
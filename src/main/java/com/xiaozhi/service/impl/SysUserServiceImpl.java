package com.xiaozhi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.common.web.PageFilter;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.config.EmailConfig;
import com.xiaozhi.dao.ConfigMapper;
import com.xiaozhi.dao.UserMapper;
import com.xiaozhi.entity.SysUser;
import com.xiaozhi.security.AuthenticationService;
import com.xiaozhi.service.SysUserService;
import com.xiaozhi.utils.DateUtils;
import com.xiaozhi.utils.ImageUtils;
import com.xiaozhi.vo.EmailCaptchaParams;
import com.xiaozhi.vo.UserCreateParams;
import com.xiaozhi.vo.UserQueryParams;
import com.xiaozhi.vo.UserUpdateParams;
import io.github.biezhi.ome.OhMyEmail;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import static io.github.biezhi.ome.OhMyEmail.SMTP_QQ;

import java.util.List;


/**
 * 用户操作
 * 
 * <AUTHOR>
 * 
 */

@Service
public class SysUserServiceImpl extends BaseServiceImpl implements SysUserService {

    private static final String dayOfMonthStart = DateUtils.dayOfMonthStart();
    private static final String dayOfMonthEnd = DateUtils.dayOfMonthEnd();

    @Resource
    private UserMapper userMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private AuthenticationService authenticationService;

    @Resource
    private EmailConfig emailConfig;

    /**
     * 用户信息查询
     * 
     * @param username
     * @return 用户信息
     */
    @Override
    public SysUser query(String username) {
        return userMapper.query(username, dayOfMonthStart, dayOfMonthEnd);
    }

    /**
     * 用户列表查询
     * 
     * @param user
     * @return 用户列表
     */
    @Override
    public List<SysUser> queryUsers(SysUser user, PageFilter pageFilter) {
        return userMapper.queryUsers(user);
    }

    @Override
    public SysUser selectUserByUserId(Integer userId) {
        return userMapper.selectUserByUserId(userId);
    }

    @Override
    public SysUser selectUserByUsername(String username) {
        return userMapper.selectUserByUsername(username);
    }

    @Override
    public SysUser selectUserByEmail(String email) {
        return userMapper.selectUserByEmail(email);
    }

    /**
     * 新增用户
     * 
     * @param user
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int add(SysUser user) {
        return userMapper.add(user);
    }

    /**
     * 用户信息更改
     * 
     * @param user
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int update(SysUser user) {
        return userMapper.update(user);
    }

    /**
     * 生成验证码
     * 
     */
    public String generateCode() {
        return "";
    }

    /**
     * 查询验证码是否有效
     *
     * @param code
     * @param email
     * @return
     */
    @Override
    public int queryCaptcha(String code, String email) {
        return userMapper.queryCaptcha(code, email);
    }

    // ========== 新的 DeviceController 风格方法实现 ==========

    /**
     * 添加用户 - 新风格
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public Either<BizError, ?> add(UserCreateParams params) {
        return Option.of(params)
                .toEither(BizError.BadRequest)
                .flatMap(p -> {
                    // 验证验证码
                    int row = userMapper.queryCaptcha(p.getCode(), p.getEmail());
                    if (row <= 0) {
                        return Either.left(BizError.of(400, "无效验证码"));
                    }

                    // 检查用户名和邮箱是否已存在
                    SysUser existingUserByName = userMapper.selectUserByUsername(p.getUsername());
                    if (existingUserByName != null) {
                        return Either.left(BizError.of(400, "用户名已存在"));
                    }

                    SysUser existingUserByEmail = userMapper.selectUserByEmail(p.getEmail());
                    if (existingUserByEmail != null) {
                        return Either.left(BizError.of(400, "邮箱已注册"));
                    }

                    // 创建用户
                    SysUser user = new SysUser();
                    user.setUsername(p.getUsername());
                    user.setEmail(p.getEmail());
                    user.setName(p.getName());
                    user.setMobile(p.getTel());
                    String encryptedPassword = authenticationService.encryptPassword(p.getPassword());
                    user.setPassword(encryptedPassword);

                    int result = userMapper.add(user);
                    if (result > 0) {
                        return Either.right(user);
                    } else {
                        return Either.left(BizError.SystemError);
                    }
                });
    }

    /**
     * 更新用户 - 新风格
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public Either<BizError, ?> update(Integer id, UserUpdateParams params) {
        return Option.of(userMapper.selectUserByUserId(id))
                .toEither(BizError.of(404, "用户不存在"))
                .map(user -> {
                    // 根据用户名或邮箱查找用户
                    SysUser targetUser = user;
                    if (StringUtils.hasText(params.getUsername())) {
                        targetUser = userMapper.selectUserByUsername(params.getUsername());
                    } else if (StringUtils.hasText(params.getEmail())) {
                        targetUser = userMapper.selectUserByEmail(params.getEmail());
                    }

                    if (targetUser == null) {
                        return Either.<BizError, Object>left(BizError.of(404, "无此用户，操作失败"));
                    }

                    // 更新密码
                    if (StringUtils.hasText(params.getPassword())) {
                        String encryptedPassword = authenticationService.encryptPassword(params.getPassword());
                        targetUser.setPassword(encryptedPassword);
                    }

                    // 更新头像
                    if (!StringUtils.hasText(params.getAvatar()) && StringUtils.hasText(params.getName())) {
                        try {
                            targetUser.setAvatar(ImageUtils.GenerateImg(params.getName()));
                        } catch (Exception e) {
                            // 头像生成失败不影响其他更新操作
                        }
                    }

                    // 更新其他字段
                    if (StringUtils.hasText(params.getName())) {
                        targetUser.setName(params.getName());
                    }

                    int result = userMapper.update(targetUser);
                    if (result > 0) {
                        return Either.<BizError, Object>right(targetUser);
                    } else {
                        return Either.<BizError, Object>left(BizError.SystemError);
                    }
                })
                .fold(Either::left, identity -> identity);
    }

    /**
     * 删除用户 - 新风格
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public Either<BizError, ?> delete(Integer id) {
        return Option.of(userMapper.selectUserByUserId(id))
                .toEither(BizError.of(404, "用户不存在"))
                .flatMap(user -> {
                    // 这里可以添加删除逻辑，目前只是示例
                    // 实际项目中可能需要软删除或者检查关联数据
                    return Either.left(BizError.of(501, "删除功能未实现"));
                });
    }

    /**
     * 用户列表 - 新风格
     */
    @Override
    public Resp findPage(UserQueryParams params) {
        try {
            Page<SysUser> page = params.toPage();

            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();

            // 添加查询条件
            params.getUsername().ifPresent(username ->
                queryWrapper.like(SysUser::getUsername, username));
            params.getEmail().ifPresent(email ->
                queryWrapper.like(SysUser::getEmail, email));
            params.getName().ifPresent(name ->
                queryWrapper.like(SysUser::getName, name));

            IPage<SysUser> result = userMapper.selectPage(page, queryWrapper);
            return Resp.from(result);
        } catch (Exception e) {
            return Resp.fail(BizError.SystemError);
        }
    }

    /**
     * 发送邮箱验证码 - 新风格
     */
    @Override
    public Either<BizError, ?> sendEmailCaptcha(EmailCaptchaParams params) {
        try {
            String email = params.getEmail();
            String type = params.getType();

            // 验证邮箱格式
            if (!isValidEmail(email)) {
                return Either.left(BizError.of(400, "邮箱格式不正确"));
            }

            SysUser user = userMapper.selectUserByEmail(email);
            if ("forget".equals(type) && user == null) {
                return Either.left(BizError.of(400, "该邮箱未注册"));
            }

            var code = generateCode();
            String emailContent = "尊敬的用户您好!您的验证码为:<h3>" + code + "</h3>如不是您操作,请忽略此邮件.(有效期10分钟)";

            // 需要配置自己的第三方邮箱认证信息
            if (!StringUtils.hasText(emailConfig.getUsername()) || !StringUtils.hasText(emailConfig.getPassword())) {
                return Either.left(BizError.of(500, "未配置第三方邮箱认证信息,请联系管理员"));
            }

            // 配置邮件发送
            OhMyEmail.config(SMTP_QQ(false), emailConfig.getUsername(), emailConfig.getPassword());

            // 发送邮件
            OhMyEmail.subject("小智ESP32-智能物联网管理平台")
                    .from("小智物联网管理平台")
                    .to(email)
                    .html(emailContent)
                    .send();

            return Either.right("验证码发送成功");
        } catch (Exception e) {
            // 根据异常类型返回不同的错误信息
            String errorMsg = "发送失败";
            if (e.getMessage() != null) {
                if (e.getMessage().contains("non-existent account") ||
                        e.getMessage().contains("550") ||
                        e.getMessage().contains("recipient")) {
                    errorMsg = "邮箱地址不存在或无效";
                } else if (e.getMessage().contains("Authentication failed")) {
                    errorMsg = "邮箱服务认证失败，请联系管理员";
                } else if (e.getMessage().contains("timed out")) {
                    errorMsg = "邮件发送超时，请稍后重试";
                }
            }
            return Either.left(BizError.of(500, errorMsg));
        }
    }

    /**
     * 验证验证码 - 新风格
     */
    @Override
    public Either<BizError, ?> checkCaptcha(String code, String email) {
        try {
            int row = userMapper.queryCaptcha(code, email);
            if (row <= 0) {
                return Either.left(BizError.of(400, "无效验证码"));
            }
            return Either.right("验证码有效");
        } catch (Exception e) {
            return Either.left(BizError.of(500, "操作失败,请联系管理员"));
        }
    }

    /**
     * 检查用户名和邮箱是否已存在 - 新风格
     */
    @Override
    public Either<BizError, ?> checkUser(String username, String email) {
        try {
            SysUser userName = userMapper.selectUserByUsername(username);
            SysUser userEmail = userMapper.selectUserByEmail(email);

            if (userName != null) {
                return Either.left(BizError.of(400, "用户名已存在"));
            } else if (userEmail != null) {
                return Either.left(BizError.of(400, "邮箱已注册"));
            }
            return Either.right("用户名和邮箱可用");
        } catch (Exception e) {
            return Either.left(BizError.of(500, "操作失败,请联系管理员"));
        }
    }

    @Override
    public Either<BizError, ?> getUserInfo(Integer id) {
        var query = new OhMyLambdaQueryWrapper<SysUser>()
                .eq(SysUser::getId, id)
                .select(SysUser::getId, SysUser::getName, SysUser::getAvatar, SysUser::getIsAdmin);

        return Option.of(userMapper.selectOne(query))
                .toEither(BizError.UserNotExists);
    }

    /**
     * 简单验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        if (email == null || email.isEmpty()) {
            return false;
        }
        // 简单的邮箱格式验证，包含@符号且@后面有.
        return email.matches("^[^@]+@[^@]+\\.[^@]+$");
    }

}
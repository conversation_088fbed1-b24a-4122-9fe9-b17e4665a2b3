package com.xiaozhi.service.impl;

import com.xiaozhi.config.ActivationCodeConfig;
import com.xiaozhi.dao.CodeMapper;
import com.xiaozhi.entity.SysCode;
import com.xiaozhi.service.ActivationService;
import com.xiaozhi.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * MySQL 版本的激活码服务实现
 * 基于 SysCode 表实现激活码的生成、验证、清理等功能
 */
@Slf4j
@Service("mysqlActivationCodeService")
public class MysqlActivationServiceImpl implements ActivationService {

    @Resource
    private CodeMapper codeMapper;

    @Resource
    private ActivationCodeConfig activationCodeConfig;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public String generateActiveCode(String deviceId, String deviceType) {
        // 检查是否已存在未过期的激活码
        var existingCode = getActivationCodeByDeviceId(deviceId);
        if (existingCode.isPresent()) return existingCode.get();

        // 生成新的激活码
        String code;
        int maxRetries = 10;
        int retries = 0;
        
        do {
            code = activationCodeConfig.getCodePrefix() + CommonUtils.generateActivationCode();
            retries++;
        } while (exists(code) && retries < maxRetries);

        if (retries >= maxRetries) {
            throw new RuntimeException("无法生成唯一的激活码，请稍后重试");
        }

        // 保存到数据库
        SysCode sysCode = new SysCode();
        sysCode.setCode(code);
        sysCode.setDeviceId(deviceId);
        sysCode.setType(deviceType);
        sysCode.setCreatedAt(new Date());

        codeMapper.insert(sysCode);
        
        log.info("为设备 {} 生成激活码: {}", deviceId, code);
        return code;
    }

    @Override
    public Optional<String> getActivationCodeByDeviceId(String deviceId) {
        var sysCode = codeMapper.findByDeviceId(deviceId);
        if (sysCode == null) {
            return Optional.empty();
        }

        // code expired
        if (new Date().after(new Date(sysCode.getCreatedAt().getTime() + TimeUnit.HOURS.toMillis(activationCodeConfig.getValidityHours())))) {
            return Optional.empty();
        }

        return Optional.of(sysCode.getCode());
    }

    @Override
    public boolean deleteActivationCode(String code) {
        try {
            SysCode sysCode = codeMapper.findByCode(code);
            if (sysCode != null) {
                sysCode.setIsDeleted(true);
                codeMapper.updateById(sysCode);
                log.info("删除激活码: {}", code);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除激活码失败: {}", code, e);
            return false;
        }
    }

    @Override
    public int cleanExpiredCodes() {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusHours(activationCodeConfig.getValidityHours());
            String expireTimeStr = expireTime.format(FORMATTER);
            int deletedCount = codeMapper.deleteExpiredCodes(expireTimeStr);
            log.info("清理过期激活码，删除数量: {}", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("清理过期激活码失败", e);
            return 0;
        }
    }

    @Override
    public boolean exists(String code) {
        return codeMapper.countByCode(code) > 0;
    }

}

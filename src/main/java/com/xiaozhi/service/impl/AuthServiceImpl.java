package com.xiaozhi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.dao.UserMapper;
import com.xiaozhi.entity.SysUser;
import com.xiaozhi.enums.UserStatus;
import com.xiaozhi.service.AuthService;
import com.xiaozhi.utils.JwtUtil;
import com.xiaozhi.vo.UserLoginParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class AuthServiceImpl implements AuthService {

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Override
    public Either<BizError, ?> login(UserLoginParams params) {
        var query = new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUsername, params.getUsername())
                .select(SysUser::getId, SysUser::getPassword, SysUser::getStatus);

        return Option.of(userMapper.selectOne(query))
                .toEither(BizError.UserNotExists)
                .filterOrElse(user -> user.getStatus() == UserStatus.Normal,
                        __ -> BizError.UserFrozen)
                .filterOrElse(user -> passwordEncoder.matches(params.getPassword(), user.getPassword()),
                        __ -> BizError.UserPasswordIncorrect)
                .map(user -> JwtUtil.authorize(user.getId(), jwtSecret));
    }

    @Override
    public Either<BizError, AuthorizedUser> verify(String token) {
        return JwtUtil.verify(token, jwtSecret)
                .flatMap(authorizedUser -> {
                    var query = new LambdaQueryWrapper<SysUser>()
                            .eq(SysUser::getId, authorizedUser.getId())
                            .select(SysUser::getId, SysUser::getStatus);
                    return Option.of(userMapper.selectOne(query))
                            .toEither(BizError.UserNotExists)
                            .filterOrElse(user -> user.getStatus() == UserStatus.Normal,
                                    __ -> BizError.UserFrozen)
                            .map(__ -> authorizedUser);
                });
    }

}

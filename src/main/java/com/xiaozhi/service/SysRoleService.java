package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.entity.SysRole;
import com.xiaozhi.vo.RoleCreateParams;
import com.xiaozhi.vo.RoleQueryParams;
import com.xiaozhi.vo.RoleUpdateParams;
import com.xiaozhi.vo.TestVoiceParams;
import io.vavr.control.Either;

/**
 * 角色查询/更新
 * 
 * <AUTHOR>
 * 
 */
public interface SysRoleService {

  SysRole selectRoleById(Integer roleId);

  /**
   * 添加角色
   */
  Either<BizError, ?> create(RoleCreateParams params);

  /**
   * 更新角色
   */
  Either<BizError, ?> update(Integer id, RoleUpdateParams params);

  /**
   * 删除角色
   */
  Either<BizError, ?> delete(Integer id);

  /**
   * 角色列表
   */
  Resp findPage(RoleQueryParams params);

  /**
   * 测试语音
   */
  Either<BizError, ?> testVoice(TestVoiceParams params);

}
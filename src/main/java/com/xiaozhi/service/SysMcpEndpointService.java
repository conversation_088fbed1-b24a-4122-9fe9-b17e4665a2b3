package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dto.McpEndpointDto;
import com.xiaozhi.entity.SysMcpEndpoint;
import io.vavr.control.Either;

import java.util.List;

/**
 * MCP endpoint service interface
 */
public interface SysMcpEndpointService {
    /**
     * 分页查询端点列表
     */
    Resp findPage();
    
    /**
     * 添加端点
     */
    Either<BizError, ?> create(McpEndpointDto dto);

    /**
     * 更新端点
     */
    Either<BizError, ?> update(Integer id, McpEndpointDto dto);

    /**
     * 删除端点（软删除）
     */
    Either<BizError, ?> delete(Integer id);

    List<SysMcpEndpoint> findEndpoints();
}

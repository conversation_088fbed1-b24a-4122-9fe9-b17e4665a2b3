package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.vo.ConfigQueryParams;
import com.xiaozhi.vo.ConfigUpdateParams;
import io.vavr.control.Either;

import java.util.Map;

/**
 * 配置
 *
 * <AUTHOR>
 */
public interface SysConfigService {

    SysConfig selectConfigById(Integer configId);

    SysConfig selectModelType(String modelType);

    SysConfig findOneOf(String provider, Integer userId);

    Resp findPage(ConfigQueryParams params);

    Either<BizError,?> update(Integer id, ConfigUpdateParams params);

    Either<BizError,?> delete(Integer id);

    Either<BizError,?> create(SysConfig config);

    Map<Integer, SysConfig> findBasic2Map(String type);

    Map<String, Map<Integer, SysConfig>> findBasic2Map();
}
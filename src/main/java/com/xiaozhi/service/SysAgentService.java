package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.entity.SysAgent;
import com.xiaozhi.vo.AgentCreateParams;
import com.xiaozhi.vo.AgentQueryParams;
import io.vavr.control.Either;

/**
 * 智能体服务接口
 * 
 * <AUTHOR>
 */
public interface SysAgentService {

    Either<BizError, ?> delete(Integer id);

    Resp findPage(AgentQueryParams params);

    Either<BizError,?> create(AgentCreateParams params);

    Either<BizError,?> update(Integer id, SysAgent params);
}
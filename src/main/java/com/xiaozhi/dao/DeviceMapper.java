package com.xiaozhi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaozhi.entity.SysDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 设备信息 数据层
 * 
 * <AUTHOR>
 * 
 */
@Mapper
public interface DeviceMapper extends BaseMapper<SysDevice> {
  List<SysDevice> query(SysDevice device);

  @Select("SELECT * FROM sys_device WHERE device_id = #{deviceId} AND is_deleted = 0")
  SysDevice selectDeviceById(@Param("deviceId") String deviceId);

  int generateCode(SysDevice device);

  SysDevice queryVerifyCode(SysDevice device);

  int updateCode(SysDevice device);

  int update(SysDevice device);

  int add(SysDevice device);

  int delete(SysDevice device);

  int insertCode(String deviceId, String code);
}
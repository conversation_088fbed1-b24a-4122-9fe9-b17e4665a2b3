package com.xiaozhi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaozhi.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 角色管理 数据层
 * 
 * <AUTHOR>
 * 
 */
@Mapper
public interface RoleMapper extends BaseMapper<SysRole> {

  @Update("UPDATE sys_role SET is_default = 1 WHERE user_id = #{userId}")
  int resetDefault(@Param("userId") Integer userId);
}
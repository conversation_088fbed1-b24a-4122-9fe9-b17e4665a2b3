package com.xiaozhi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaozhi.entity.SysCode;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CodeMapper extends BaseMapper<SysCode> {

    @Select("SELECT * FROM sys_code WHERE code = #{code} AND is_deleted = 0")
    SysCode findByCode(@Param("code") String code);

    @Select("SELECT * FROM sys_code WHERE device_id = #{deviceId} AND is_deleted = 0 ORDER BY created_at DESC LIMIT 1")
    SysCode findByDeviceId(@Param("deviceId") String deviceId);

    @Delete("DELETE FROM sys_code WHERE created_at < #{expireTime}")
    int deleteExpiredCodes(@Param("expireTime") String expireTime);

    @Select("SELECT COUNT(*) FROM sys_code WHERE code = #{code} AND is_deleted = 0")
    int countByCode(@Param("code") String code);

}

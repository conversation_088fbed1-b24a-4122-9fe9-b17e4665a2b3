# 开发环境配置

# 数据库连接配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: root
    password: qweQWE!@#
  
  # 使用 Simple 缓存
  cache:
    type: simple

# 开发环境邮箱配置
email:
  smtp:
    username: xxx
    password: xxx

# 开发环境服务器地址配置示例
xiaozhi:
  server:
    address:
      host: "*************"  # 开发环境可以手动指定IP
      # port: 8091             # 开发环境可以使用不同端口
      ssl: false
    websocket:
      ssl: false
    ota:
      ssl: false
